#!/usr/bin/env python3
"""
详细对比新旧版本的差异
"""

import json
import pandas as pd
from pathlib import Path
import numpy as np
from collections import defaultdict

def analyze_single_log(filepath):
    """分析单个日志文件"""
    with open(filepath, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    metadata = data.get('metadata', {})
    metrics = data.get('metrics', {})
    results = data.get('results', [])
    
    # 基本信息
    info = {
        'model': metadata.get('model', 'unknown'),
        'dataset': metadata.get('dataset', 'unknown'),
        'num_samples': len(results),
        'accuracy': metrics.get('accuracy', 0),
        'precision': metrics.get('precision', 0),
        'recall': metrics.get('recall', 0),
        'f1': metrics.get('f1', 0)
    }
    
    # 行为分析
    layer2_triggered = 0
    confidences = []
    ambiguity_scores = []
    decision_paths = defaultdict(int)
    
    for result in results:
        if result.get('layer2_triggered', False):
            layer2_triggered += 1
        
        if 'confidence' in result:
            confidences.append(result['confidence'])
        
        if 'ambiguity_score' in result:
            ambiguity_scores.append(result['ambiguity_score'])
        
        # 决策路径分析
        if 'layer1_results' in result and 'fusion_result' in result['layer1_results']:
            path = result['layer1_results']['fusion_result'].get('decision_path', 'unknown')
            decision_paths[path] += 1
    
    info.update({
        'layer2_trigger_rate': layer2_triggered / len(results) if results else 0,
        'avg_confidence': np.mean(confidences) if confidences else 0,
        'confidence_std': np.std(confidences) if confidences else 0,
        'avg_ambiguity': np.mean(ambiguity_scores) if ambiguity_scores else 0,
        'ambiguity_std': np.std(ambiguity_scores) if ambiguity_scores else 0,
        'decision_paths': dict(decision_paths)
    })
    
    return info

def main():
    print("=" * 80)
    print("详细版本对比分析")
    print("=" * 80)
    
    # 分析当前版本
    current_results = {}
    logs_dir = Path('logs')
    for log_file in logs_dir.glob('twolayerdetectionsystem_*.json'):
        info = analyze_single_log(log_file)
        key = f"{info['model']}_{info['dataset']}"
        current_results[key] = info
        current_results[key]['version'] = 'current'
        current_results[key]['file'] = str(log_file)
    
    # 分析之前版本
    previous_results = {}
    prev_dir = Path('previous_log')
    for log_file in prev_dir.glob('twolayerdetectionsystem_*.json'):
        info = analyze_single_log(log_file)
        key = f"{info['model']}_{info['dataset']}"
        previous_results[key] = info
        previous_results[key]['version'] = 'previous'
        previous_results[key]['file'] = str(log_file)
    
    print(f"当前版本: {len(current_results)} 个结果")
    print(f"之前版本: {len(previous_results)} 个结果")
    
    # 对比分析
    comparisons = []
    
    for key in current_results:
        if key in previous_results:
            curr = current_results[key]
            prev = previous_results[key]
            
            comparison = {
                'model_dataset': key,
                'model': curr['model'],
                'dataset': curr['dataset'],
                # 性能变化
                'f1_change': curr['f1'] - prev['f1'],
                'accuracy_change': curr['accuracy'] - prev['accuracy'],
                'precision_change': curr['precision'] - prev['precision'],
                'recall_change': curr['recall'] - prev['recall'],
                # 行为变化
                'layer2_trigger_change': curr['layer2_trigger_rate'] - prev['layer2_trigger_rate'],
                'confidence_change': curr['avg_confidence'] - prev['avg_confidence'],
                'ambiguity_change': curr['avg_ambiguity'] - prev['avg_ambiguity'],
                # 原始数据
                'current': curr,
                'previous': prev
            }
            
            comparisons.append(comparison)
    
    # 生成报告
    print("\n" + "=" * 60)
    print("性能变化分析")
    print("=" * 60)
    
    # 按F1下降排序
    comparisons.sort(key=lambda x: x['f1_change'])
    
    for comp in comparisons:
        print(f"\n{comp['model_dataset']}:")
        print(f"  F1变化: {comp['f1_change']:+.4f} ({comp['previous']['f1']:.4f} -> {comp['current']['f1']:.4f})")
        print(f"  准确率变化: {comp['accuracy_change']:+.4f}")
        print(f"  召回率变化: {comp['recall_change']:+.4f}")
        print(f"  Layer2触发率变化: {comp['layer2_trigger_change']:+.4f}")
        print(f"  置信度变化: {comp['confidence_change']:+.4f}")
        print(f"  歧义分数变化: {comp['ambiguity_change']:+.4f}")
    
    # 问题模式分析
    print("\n" + "=" * 60)
    print("问题模式识别")
    print("=" * 60)
    
    # 性能下降案例
    performance_drops = [c for c in comparisons if c['f1_change'] < -0.001]
    print(f"\n性能下降案例 ({len(performance_drops)} 个):")
    for comp in performance_drops:
        print(f"  {comp['model_dataset']}: F1下降 {comp['f1_change']:.4f}")
    
    # Layer2触发率异常变化
    trigger_changes = [c for c in comparisons if abs(c['layer2_trigger_change']) > 0.05]
    print(f"\nLayer2触发率显著变化 ({len(trigger_changes)} 个):")
    for comp in trigger_changes:
        direction = "增加" if comp['layer2_trigger_change'] > 0 else "减少"
        print(f"  {comp['model_dataset']}: {direction} {abs(comp['layer2_trigger_change']):.4f}")
    
    # 置信度下降
    confidence_drops = [c for c in comparisons if c['confidence_change'] < -0.01]
    print(f"\n置信度显著下降 ({len(confidence_drops)} 个):")
    for comp in confidence_drops:
        print(f"  {comp['model_dataset']}: 下降 {abs(comp['confidence_change']):.4f}")
    
    # 歧义分数变化
    ambiguity_changes = [c for c in comparisons if abs(c['ambiguity_change']) > 0.1]
    print(f"\n歧义分数显著变化 ({len(ambiguity_changes)} 个):")
    for comp in ambiguity_changes:
        direction = "增加" if comp['ambiguity_change'] > 0 else "减少"
        print(f"  {comp['model_dataset']}: {direction} {abs(comp['ambiguity_change']):.4f}")
    
    # 决策路径变化分析
    print("\n" + "=" * 60)
    print("决策路径变化分析")
    print("=" * 60)
    
    for comp in comparisons:
        curr_paths = comp['current']['decision_paths']
        prev_paths = comp['previous']['decision_paths']
        
        # 找出显著变化的路径
        all_paths = set(curr_paths.keys()) | set(prev_paths.keys())
        significant_changes = []
        
        total_curr = comp['current']['num_samples']
        total_prev = comp['previous']['num_samples']
        
        for path in all_paths:
            curr_count = curr_paths.get(path, 0)
            prev_count = prev_paths.get(path, 0)
            
            curr_pct = curr_count / total_curr * 100 if total_curr > 0 else 0
            prev_pct = prev_count / total_prev * 100 if total_prev > 0 else 0
            
            if abs(curr_pct - prev_pct) > 5:  # 变化超过5%
                significant_changes.append((path, prev_pct, curr_pct, curr_pct - prev_pct))
        
        if significant_changes:
            print(f"\n{comp['model_dataset']} 决策路径变化:")
            for path, prev_pct, curr_pct, change in significant_changes:
                print(f"  {path}: {prev_pct:.1f}% -> {curr_pct:.1f}% ({change:+.1f}%)")
    
    # 保存详细结果
    with open('detailed_comparison_results.json', 'w', encoding='utf-8') as f:
        json.dump(comparisons, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n详细对比结果已保存到 detailed_comparison_results.json")
    
    return comparisons

if __name__ == "__main__":
    comparisons = main()
