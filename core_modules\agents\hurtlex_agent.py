#!/usr/bin/env python3
"""
HurtLex敏感词词典智能体实现
基于HurtLex多语言攻击性词汇词典的仇恨言论检测智能体
"""

import pandas as pd
import re
import time
import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class HurtLexConfig:
    """HurtLex智能体配置"""
    lexicon_path: str = "word_dic/hurtlex/hurtlex_EN.tsv"
    language: str = "EN"  # 语言代码
    level: str = "both"  # conservative, inclusive, both
    cache_size: int = 1000
    case_sensitive: bool = False
    use_stemming: bool = False
    confidence_threshold: float = 0.3  # 降低阈值，提高敏感度
    category_weights: Dict[str, float] = None  # 类别权重

class HurtLexAgent:
    """
    HurtLex敏感词词典智能体
    基于HurtLex多语言攻击性词汇词典
    支持17个攻击性词汇类别的检测
    """

    def __init__(self, config: HurtLexConfig = None):
        """
        初始化HurtLex智能体

        Args:
            config: 配置对象
        """
        self.config = config or HurtLexConfig()

        # 设置默认类别权重
        if self.config.category_weights is None:
            self.config.category_weights = {
                "PS": 1.0,   # negative stereotypes ethnic slurs
                "RCI": 0.6,  # locations and demonyms
                "PA": 0.4,   # professions and occupations
                "DDF": 0.8,  # physical disabilities and diversity
                "DDP": 0.8,  # cognitive disabilities and diversity
                "DMC": 0.9,  # moral and behavioral defects
                "IS": 0.7,   # social and economic disadvantage
                "OR": 0.3,   # plants
                "AN": 0.5,   # animals
                "ASM": 0.9,  # male genitalia
                "ASF": 0.9,  # female genitalia
                "PR": 0.9,   # prostitution
                "OM": 0.9,   # homosexuality
                "QAS": 0.6,  # potential negative connotations
                "CDS": 0.8,  # derogatory words
                "RE": 0.9,   # felonies and crime
                "SVP": 0.7   # seven deadly sins
            }

        logger.info(f"HurtLex智能体初始化，语言: {self.config.language}")

        # 预测缓存
        self.prediction_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0

        # 性能统计
        self.total_predictions = 0
        self.total_processing_time = 0.0

        # 词典数据
        self.lexicon_data = None
        self.offensive_words = set()
        self.word_categories = {}
        self.word_levels = {}

        # 加载词典
        self._load_lexicon()

    def _load_lexicon(self):
        """加载HurtLex词典"""
        try:
            logger.info(f"正在加载HurtLex词典: {self.config.lexicon_path}")

            # 读取TSV文件
            self.lexicon_data = pd.read_csv(self.config.lexicon_path, sep='\t')
            logger.info(f"✅ 词典加载成功，共 {len(self.lexicon_data)} 条记录")

            # 根据配置过滤词汇
            filtered_data = self.lexicon_data.copy()
            
            if self.config.level == "conservative":
                filtered_data = filtered_data[filtered_data['level'] == 'conservative']
            elif self.config.level == "inclusive":
                filtered_data = filtered_data[filtered_data['level'] == 'inclusive']
            # both 情况下不过滤

            logger.info(f"过滤后词汇数量: {len(filtered_data)}")

            # 构建词汇集合和映射
            for _, row in filtered_data.iterrows():
                word = row['lemma'].strip()
                if not self.config.case_sensitive:
                    word = word.lower()
                
                self.offensive_words.add(word)
                self.word_categories[word] = row['category']
                self.word_levels[word] = row['level']

            logger.info(f"✅ 构建完成，攻击性词汇总数: {len(self.offensive_words)}")

            # 统计各类别词汇数量
            category_counts = {}
            for category in self.word_categories.values():
                category_counts[category] = category_counts.get(category, 0) + 1
            
            logger.info("各类别词汇分布:")
            for category, count in sorted(category_counts.items()):
                logger.info(f"  {category}: {count} 词汇")

        except Exception as e:
            logger.error(f"❌ HurtLex词典加载失败: {e}")
            raise

    def _preprocess_text(self, text: str) -> List[str]:
        """预处理文本，提取词汇"""
        if not self.config.case_sensitive:
            text = text.lower()
        
        # 简单的词汇分割（可以根据需要改进）
        # 移除标点符号，保留字母和数字
        words = re.findall(r'\b[a-zA-Z]+\b', text)
        
        return words

    def _calculate_offensiveness_score(self, matched_words: List[str]) -> float:
        """计算攻击性分数"""
        if not matched_words:
            return 0.0
        
        total_score = 0.0
        for word in matched_words:
            category = self.word_categories.get(word, "unknown")
            weight = self.config.category_weights.get(category, 0.5)
            
            # 基础分数
            base_score = 1.0
            
            # 根据级别调整分数
            level = self.word_levels.get(word, "inclusive")
            if level == "conservative":
                base_score *= 1.2  # 保守级别词汇权重更高
            
            total_score += base_score * weight
        
        # 归一化到0-1范围
        max_possible_score = len(matched_words) * 1.2  # 假设都是最高权重
        normalized_score = min(total_score / max_possible_score, 1.0) if max_possible_score > 0 else 0.0
        
        return normalized_score

    def detect(self, text: str, method: str = "lexicon_matching") -> Dict:
        """
        检测文本是否包含攻击性词汇

        Args:
            text: 输入文本
            method: 检测方法

        Returns:
            检测结果字典
        """
        # 检查缓存
        cache_key = f"hurtlex_{hash(text)}"
        if cache_key in self.prediction_cache:
            self.cache_hits += 1
            cached_result = self.prediction_cache[cache_key].copy()
            cached_result["from_cache"] = True
            return cached_result

        self.cache_misses += 1
        start_time = time.time()

        try:
            # 预处理文本
            words = self._preprocess_text(text)
            
            # 查找匹配的攻击性词汇
            matched_words = []
            matched_categories = {}
            
            for word in words:
                if word in self.offensive_words:
                    matched_words.append(word)
                    category = self.word_categories[word]
                    if category not in matched_categories:
                        matched_categories[category] = []
                    matched_categories[category].append(word)

            # 计算攻击性分数
            offensiveness_score = self._calculate_offensiveness_score(matched_words)
            
            # 判断是否为攻击性内容
            verdict = 1 if offensiveness_score >= self.config.confidence_threshold else 0
            confidence = offensiveness_score

            processing_time = time.time() - start_time

            # 构建详细的推理说明
            reasoning_parts = []
            if matched_words:
                reasoning_parts.append(f"检测到 {len(matched_words)} 个攻击性词汇: {', '.join(matched_words)}")
                for category, words in matched_categories.items():
                    category_desc = self._get_category_description(category)
                    reasoning_parts.append(f"类别 {category} ({category_desc}): {', '.join(words)}")
                reasoning_parts.append(f"攻击性分数: {offensiveness_score:.3f}")
            else:
                reasoning_parts.append("未检测到攻击性词汇")

            reasoning = "; ".join(reasoning_parts)

            # 构建结果
            result = {
                "verdict": verdict,
                "confidence": confidence,
                "offensiveness_score": offensiveness_score,
                "matched_words": matched_words,
                "matched_categories": matched_categories,
                "total_words_checked": len(words),
                "processing_time": processing_time,
                "method": "hurtlex_lexicon_matching",
                "from_cache": False,
                "reasoning": reasoning
            }

            # 缓存结果
            if len(self.prediction_cache) < self.config.cache_size:
                self.prediction_cache[cache_key] = result.copy()

            # 更新统计
            self.total_predictions += 1
            self.total_processing_time += processing_time

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"HurtLex检测错误: {e}")

            return {
                "verdict": 0,
                "confidence": 0.0,
                "error": str(e),
                "processing_time": processing_time,
                "method": "hurtlex_lexicon_matching",
                "from_cache": False,
                "reasoning": f"检测失败: {str(e)}"
            }

    def _get_category_description(self, category: str) -> str:
        """获取类别描述"""
        descriptions = {
            "ps": "种族诽谤",
            "rci": "地点和民族",
            "pa": "职业",
            "ddf": "身体残疾",
            "ddp": "认知残疾",
            "dmc": "道德缺陷",
            "is": "社会经济劣势",
            "or": "植物",
            "an": "动物",
            "asm": "男性生殖器",
            "asf": "女性生殖器",
            "pr": "卖淫",
            "om": "同性恋",
            "qas": "潜在负面含义",
            "cds": "贬义词",
            "re": "犯罪",
            "svp": "七宗罪"
        }
        return descriptions.get(category.lower(), "未知类别")

    def batch_detect(self, texts: List[str]) -> List[Dict]:
        """批量检测"""
        results = []
        for text in texts:
            result = self.detect(text)
            results.append(result)
        return results

    def get_statistics(self) -> Dict:
        """获取性能统计"""
        total_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0
        avg_processing_time = self.total_processing_time / self.total_predictions if self.total_predictions > 0 else 0

        return {
            "total_predictions": self.total_predictions,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_rate": cache_hit_rate,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time,
            "lexicon_size": len(self.offensive_words),
            "categories_count": len(set(self.word_categories.values()))
        }

    def clear_cache(self):
        """清除缓存"""
        self.prediction_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("HurtLex智能体缓存已清除")

    def get_category_statistics(self) -> Dict:
        """获取类别统计信息"""
        category_counts = {}
        for category in self.word_categories.values():
            category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            "category_counts": category_counts,
            "category_descriptions": {cat: self._get_category_description(cat) for cat in category_counts.keys()}
        }
