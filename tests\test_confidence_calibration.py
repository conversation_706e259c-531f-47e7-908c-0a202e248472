#!/usr/bin/env python3
"""
自适应置信度校准模块单元测试
"""

import unittest
import numpy as np
import tempfile
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_modules.confidence_calibration import (
    CalibrationConfig,
    TemperatureScaling,
    UncertaintyQuantifier,
    AdaptiveThresholdManager,
    AdaptiveConfidenceCalibrator
)

class TestTemperatureScaling(unittest.TestCase):
    """温度缩放测试"""
    
    def setUp(self):
        self.temp_scaler = TemperatureScaling()
    
    def test_calibrate_logits(self):
        """测试logits校准"""
        logits = np.array([2.0, 1.0])  # 简单的二分类logits
        
        # 测试默认温度
        probs = self.temp_scaler.calibrate_logits(logits, 'toxigen')
        self.assertEqual(len(probs), 2)
        self.assertAlmostEqual(np.sum(probs), 1.0, places=6)
        self.assertTrue(probs[0] > probs[1])  # 第一个类别概率更高
    
    def test_temperature_optimization(self):
        """测试温度参数优化"""
        # 创建模拟数据
        logits_list = [
            np.array([2.0, 1.0]),
            np.array([1.5, 2.0]),
            np.array([3.0, 0.5]),
            np.array([0.8, 1.2])
        ]
        true_labels = [0, 1, 0, 1]
        
        # 优化温度
        optimal_temp = self.temp_scaler.optimize_temperature(logits_list, true_labels, 'toxigen')
        
        self.assertIsInstance(optimal_temp, float)
        self.assertGreater(optimal_temp, 0.1)
        self.assertLess(optimal_temp, 10.0)
    
    def test_temperature_getter_setter(self):
        """测试温度参数的获取和设置"""
        # 设置温度
        self.temp_scaler.set_temperature('toxigen', 1.5)
        
        # 获取温度
        temp = self.temp_scaler.get_temperature('toxigen')
        self.assertEqual(temp, 1.5)
        
        # 测试未知模型类型
        temp_unknown = self.temp_scaler.get_temperature('unknown_model')
        self.assertEqual(temp_unknown, 1.0)  # 默认温度

class TestUncertaintyQuantifier(unittest.TestCase):
    """不确定性量化测试"""
    
    def setUp(self):
        self.uncertainty_quantifier = UncertaintyQuantifier()
    
    def test_prediction_entropy(self):
        """测试预测熵计算"""
        # 确定性预测（低熵）
        certain_pred = np.array([0.9, 0.1])
        entropy_certain = self.uncertainty_quantifier.calculate_prediction_entropy(certain_pred)
        
        # 不确定性预测（高熵）
        uncertain_pred = np.array([0.5, 0.5])
        entropy_uncertain = self.uncertainty_quantifier.calculate_prediction_entropy(uncertain_pred)
        
        self.assertGreater(entropy_uncertain, entropy_certain)
        self.assertGreaterEqual(entropy_certain, 0)
        self.assertLessEqual(entropy_uncertain, 1.0)  # 二分类最大熵为1
    
    def test_kl_divergence(self):
        """测试KL散度计算"""
        p = np.array([0.7, 0.3])
        q = np.array([0.6, 0.4])
        
        kl_div = self.uncertainty_quantifier.calculate_kl_divergence(p, q)
        
        self.assertGreaterEqual(kl_div, 0)  # KL散度非负
        
        # 相同分布的KL散度应该为0
        kl_same = self.uncertainty_quantifier.calculate_kl_divergence(p, p)
        self.assertAlmostEqual(kl_same, 0, places=6)
    
    def test_model_disagreement(self):
        """测试模型分歧度量"""
        # 相似预测
        pred1 = np.array([0.7, 0.3])
        pred2 = np.array([0.6, 0.4])
        disagreement_low = self.uncertainty_quantifier.calculate_model_disagreement(pred1, pred2)
        
        # 分歧预测
        pred3 = np.array([0.8, 0.2])
        pred4 = np.array([0.2, 0.8])
        disagreement_high = self.uncertainty_quantifier.calculate_model_disagreement(pred3, pred4)
        
        self.assertGreater(disagreement_high, disagreement_low)
    
    def test_uncertainty_quantification(self):
        """测试综合不确定性量化"""
        toxigen_pred = np.array([0.7, 0.3])
        hurtlex_pred = np.array([0.6, 0.4])
        
        uncertainty_metrics = self.uncertainty_quantifier.quantify_uncertainty(toxigen_pred, hurtlex_pred)
        
        # 检查返回的指标
        expected_keys = [
            'toxigen_entropy', 'hurtlex_entropy', 'ensemble_entropy',
            'model_disagreement', 'epistemic_uncertainty', 'aleatoric_uncertainty',
            'total_uncertainty'
        ]
        
        for key in expected_keys:
            self.assertIn(key, uncertainty_metrics)
            self.assertIsInstance(uncertainty_metrics[key], float)
            self.assertGreaterEqual(uncertainty_metrics[key], 0)

class TestAdaptiveThresholdManager(unittest.TestCase):
    """自适应阈值管理器测试"""
    
    def setUp(self):
        self.threshold_manager = AdaptiveThresholdManager()
    
    def test_adaptive_threshold_calculation(self):
        """测试自适应阈值计算"""
        confidence_scores = [0.6, 0.7, 0.8, 0.5, 0.9]
        uncertainty_metrics = {
            'total_uncertainty': 0.3,
            'ensemble_entropy': 0.8
        }
        
        threshold = self.threshold_manager.calculate_adaptive_threshold(
            confidence_scores, uncertainty_metrics
        )
        
        self.assertIsInstance(threshold, float)
        self.assertGreaterEqual(threshold, 0.5)
        self.assertLessEqual(threshold, 0.9)
    
    def test_layer2_trigger_decision(self):
        """测试第二层触发决策"""
        uncertainty_metrics = {
            'total_uncertainty': 0.3,
            'ensemble_entropy': 0.8,
            'model_disagreement': 0.2
        }
        
        # 低置信度情况
        should_trigger_low, reason_low = self.threshold_manager.should_trigger_layer2(
            0.5, 0.4, uncertainty_metrics, False
        )
        
        # 高置信度情况
        should_trigger_high, reason_high = self.threshold_manager.should_trigger_layer2(
            0.9, 0.8, uncertainty_metrics, False
        )
        
        # 模型分歧情况
        should_trigger_disagree, reason_disagree = self.threshold_manager.should_trigger_layer2(
            0.8, 0.7, uncertainty_metrics, True
        )
        
        self.assertIsInstance(should_trigger_low, bool)
        self.assertIsInstance(reason_low, str)
        
        # 低置信度更可能触发第二层
        if should_trigger_low and not should_trigger_high:
            self.assertTrue(True)  # 符合预期
    
    def test_performance_history_update(self):
        """测试性能历史更新"""
        performance_metrics = {'f1': 0.8, 'accuracy': 0.75}
        
        initial_size = len(self.threshold_manager.performance_history)
        self.threshold_manager.update_performance_history(performance_metrics)
        
        self.assertEqual(len(self.threshold_manager.performance_history), initial_size + 1)
        
        # 测试获取最近性能
        recent_perf = self.threshold_manager.get_recent_performance(window_size=1)
        if recent_perf:
            self.assertEqual(recent_perf['f1'], 0.8)
            self.assertEqual(recent_perf['accuracy'], 0.75)

class TestAdaptiveConfidenceCalibrator(unittest.TestCase):
    """自适应置信度校准器测试"""
    
    def setUp(self):
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        config = CalibrationConfig()
        config.calibration_data_path = self.temp_dir
        self.calibrator = AdaptiveConfidenceCalibrator(config)
    
    def tearDown(self):
        # 清理临时目录
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_calibrate_predictions(self):
        """测试预测校准"""
        toxigen_logits = np.array([2.0, 1.0])
        hurtlex_logits = np.array([1.5, 1.8])
        
        toxigen_cal, hurtlex_cal = self.calibrator.calibrate_predictions(
            toxigen_logits, hurtlex_logits
        )
        
        # 检查输出格式
        self.assertEqual(len(toxigen_cal), 2)
        self.assertEqual(len(hurtlex_cal), 2)
        self.assertAlmostEqual(np.sum(toxigen_cal), 1.0, places=6)
        self.assertAlmostEqual(np.sum(hurtlex_cal), 1.0, places=6)
    
    def test_analyze_ambiguity(self):
        """测试歧义分析"""
        toxigen_pred = np.array([0.7, 0.3])
        hurtlex_pred = np.array([0.4, 0.6])  # 分歧预测
        
        analysis = self.calibrator.analyze_ambiguity(toxigen_pred, hurtlex_pred)
        
        # 检查返回的字段
        expected_keys = [
            'should_trigger_layer2', 'trigger_reason', 'ambiguity_score',
            'uncertainty_metrics', 'model_disagreement', 'toxigen_confidence',
            'hurtlex_confidence', 'current_threshold'
        ]
        
        for key in expected_keys:
            self.assertIn(key, analysis)
        
        # 分歧预测应该触发第二层
        self.assertTrue(analysis['model_disagreement'])
        self.assertIsInstance(analysis['ambiguity_score'], float)
        self.assertGreaterEqual(analysis['ambiguity_score'], 0)
        self.assertLessEqual(analysis['ambiguity_score'], 1)
    
    def test_calibration_update(self):
        """测试校准更新"""
        toxigen_logits = np.array([2.0, 1.0])
        hurtlex_logits = np.array([1.5, 1.8])
        true_label = 0
        performance_metrics = {'f1': 0.8, 'accuracy': 0.75}
        
        initial_data_size = len(self.calibrator.calibration_data)
        
        self.calibrator.update_calibration(
            toxigen_logits, hurtlex_logits, true_label, performance_metrics
        )
        
        # 检查数据是否被添加
        self.assertEqual(len(self.calibrator.calibration_data), initial_data_size + 1)
    
    def test_save_load_calibration_data(self):
        """测试校准数据保存和加载"""
        # 添加一些校准数据
        toxigen_logits = np.array([2.0, 1.0])
        hurtlex_logits = np.array([1.5, 1.8])
        self.calibrator.update_calibration(toxigen_logits, hurtlex_logits, 0)
        
        # 保存数据
        filepath = os.path.join(self.temp_dir, "test_calibration.json")
        self.calibrator.save_calibration_data(filepath)
        
        # 检查文件是否存在
        self.assertTrue(os.path.exists(filepath))
        
        # 创建新的校准器并加载数据
        new_calibrator = AdaptiveConfidenceCalibrator(self.calibrator.config)
        new_calibrator.load_calibration_data(filepath)
        
        # 检查数据是否正确加载
        self.assertEqual(len(new_calibrator.calibration_data), 1)
    
    def test_calibration_stats(self):
        """测试校准统计信息"""
        stats = self.calibrator.get_calibration_stats()
        
        expected_keys = [
            'temperatures', 'current_threshold', 'calibration_samples',
            'performance_history_size', 'recent_performance'
        ]
        
        for key in expected_keys:
            self.assertIn(key, stats)
        
        self.assertIsInstance(stats['temperatures'], dict)
        self.assertIsInstance(stats['current_threshold'], float)
        self.assertIsInstance(stats['calibration_samples'], int)

def run_quick_test():
    """运行快速功能测试"""
    print("运行自适应置信度校准模块快速测试...")
    
    # 创建校准器
    calibrator = AdaptiveConfidenceCalibrator()
    
    # 模拟数据
    toxigen_logits = np.array([2.0, 1.0])
    hurtlex_logits = np.array([1.5, 1.8])
    
    # 测试校准
    toxigen_cal, hurtlex_cal = calibrator.calibrate_predictions(toxigen_logits, hurtlex_logits)
    print(f"校准前 ToxiGen logits: {toxigen_logits}")
    print(f"校准后 ToxiGen 概率: {toxigen_cal}")
    print(f"校准前 HurtLex logits: {hurtlex_logits}")
    print(f"校准后 HurtLex 概率: {hurtlex_cal}")
    
    # 测试歧义分析
    analysis = calibrator.analyze_ambiguity(toxigen_cal, hurtlex_cal)
    print(f"\n歧义分析结果:")
    print(f"是否触发第二层: {analysis['should_trigger_layer2']}")
    print(f"触发原因: {analysis['trigger_reason']}")
    print(f"歧义分数: {analysis['ambiguity_score']:.4f}")
    print(f"模型分歧: {analysis['model_disagreement']}")
    
    # 测试校准更新
    calibrator.update_calibration(toxigen_logits, hurtlex_logits, 0)
    
    # 获取统计信息
    stats = calibrator.get_calibration_stats()
    print(f"\n校准统计信息:")
    print(f"温度参数: {stats['temperatures']}")
    print(f"当前阈值: {stats['current_threshold']:.4f}")
    print(f"校准样本数: {stats['calibration_samples']}")
    
    print("\n✅ 快速测试完成！")

if __name__ == '__main__':
    # 运行快速测试
    run_quick_test()
    
    # 运行完整单元测试
    print("\n" + "="*50)
    print("运行完整单元测试...")
    unittest.main(verbosity=2)
