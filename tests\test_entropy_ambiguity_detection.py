#!/usr/bin/env python3
"""
基于熵的歧义检测模块单元测试
"""

import unittest
import numpy as np
import tempfile
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_modules.entropy_ambiguity_detection import (
    EntropyDetectionConfig,
    EntropyCalculator,
    ModelDisagreementMeasure,
    BayesianUncertaintyEstimator,
    EntropyBasedAmbiguityDetector
)

class TestEntropyCalculator(unittest.TestCase):
    """熵计算器测试"""
    
    def setUp(self):
        self.entropy_calc = EntropyCalculator()
    
    def test_shannon_entropy(self):
        """测试Shannon熵计算"""
        # 确定性分布（低熵）
        certain_dist = np.array([0.9, 0.1])
        entropy_certain = self.entropy_calc.calculate_shannon_entropy(certain_dist)
        
        # 均匀分布（高熵）
        uniform_dist = np.array([0.5, 0.5])
        entropy_uniform = self.entropy_calc.calculate_shannon_entropy(uniform_dist)
        
        self.assertGreater(entropy_uniform, entropy_certain)
        self.assertAlmostEqual(entropy_uniform, 1.0, places=6)  # 二分类均匀分布熵为1
        self.assertGreaterEqual(entropy_certain, 0)
    
    def test_kl_divergence(self):
        """测试KL散度计算"""
        p = np.array([0.7, 0.3])
        q = np.array([0.6, 0.4])
        
        kl_div = self.entropy_calc.calculate_kl_divergence(p, q)
        
        self.assertGreaterEqual(kl_div, 0)  # KL散度非负
        
        # 相同分布的KL散度应该为0
        kl_same = self.entropy_calc.calculate_kl_divergence(p, p)
        self.assertAlmostEqual(kl_same, 0, places=6)
    
    def test_js_divergence(self):
        """测试Jensen-Shannon散度"""
        p = np.array([0.8, 0.2])
        q = np.array([0.3, 0.7])
        
        js_div = self.entropy_calc.calculate_js_divergence(p, q)
        
        self.assertGreaterEqual(js_div, 0)
        self.assertLessEqual(js_div, 1.0)  # JS散度有上界
        
        # 相同分布的JS散度应该为0
        js_same = self.entropy_calc.calculate_js_divergence(p, p)
        self.assertAlmostEqual(js_same, 0, places=6)
    
    def test_ensemble_entropy(self):
        """测试集成熵计算"""
        predictions = [
            np.array([0.7, 0.3]),
            np.array([0.6, 0.4]),
            np.array([0.8, 0.2])
        ]
        
        ensemble_entropy = self.entropy_calc.calculate_ensemble_entropy(predictions)
        
        self.assertIsInstance(ensemble_entropy, float)
        self.assertGreaterEqual(ensemble_entropy, 0)
        self.assertLessEqual(ensemble_entropy, 1.0)
    
    def test_mutual_information(self):
        """测试互信息计算"""
        pred1 = np.array([0.7, 0.3])
        pred2 = np.array([0.6, 0.4])
        
        mutual_info = self.entropy_calc.calculate_mutual_information(pred1, pred2)
        
        self.assertIsInstance(mutual_info, float)
        self.assertGreaterEqual(mutual_info, 0)

class TestModelDisagreementMeasure(unittest.TestCase):
    """模型分歧度量测试"""
    
    def setUp(self):
        self.disagreement_measure = ModelDisagreementMeasure()
    
    def test_prediction_disagreement(self):
        """测试预测分歧度量"""
        # 相似预测
        pred1 = np.array([0.7, 0.3])
        pred2 = np.array([0.6, 0.4])
        
        disagreement_low = self.disagreement_measure.measure_prediction_disagreement(pred1, pred2)
        
        # 分歧预测
        pred3 = np.array([0.8, 0.2])
        pred4 = np.array([0.2, 0.8])
        
        disagreement_high = self.disagreement_measure.measure_prediction_disagreement(pred3, pred4)
        
        # 检查返回的指标
        expected_keys = [
            'kl_p_to_q', 'kl_q_to_p', 'symmetric_kl', 'js_divergence',
            'l2_distance', 'l1_distance', 'cosine_disagreement',
            'class_disagreement', 'confidence_gap'
        ]
        
        for key in expected_keys:
            self.assertIn(key, disagreement_low)
            self.assertIn(key, disagreement_high)
        
        # 高分歧应该有更大的分歧度量
        self.assertGreater(disagreement_high['symmetric_kl'], disagreement_low['symmetric_kl'])
        self.assertGreater(disagreement_high['class_disagreement'], disagreement_low['class_disagreement'])
    
    def test_disagreement_patterns(self):
        """测试分歧模式分析"""
        # 创建模拟分歧历史
        disagreement_history = []
        for i in range(10):
            pred1 = np.array([0.7 + i*0.01, 0.3 - i*0.01])
            pred2 = np.array([0.6 - i*0.01, 0.4 + i*0.01])
            disagreement = self.disagreement_measure.measure_prediction_disagreement(pred1, pred2)
            disagreement_history.append(disagreement)
        
        patterns = self.disagreement_measure.analyze_disagreement_patterns(disagreement_history)
        
        # 检查统计信息
        self.assertIn('symmetric_kl_mean', patterns)
        self.assertIn('symmetric_kl_std', patterns)
        self.assertIn('disagreement_trend', patterns)

class TestBayesianUncertaintyEstimator(unittest.TestCase):
    """贝叶斯不确定性估计测试"""
    
    def setUp(self):
        self.uncertainty_estimator = BayesianUncertaintyEstimator()
    
    def test_mc_dropout_uncertainty(self):
        """测试Monte Carlo Dropout不确定性估计"""
        # 模拟多次dropout采样结果
        mc_predictions = [
            np.array([0.7, 0.3]),
            np.array([0.65, 0.35]),
            np.array([0.75, 0.25]),
            np.array([0.68, 0.32]),
            np.array([0.72, 0.28])
        ]
        
        uncertainty = self.uncertainty_estimator.estimate_mc_dropout_uncertainty(mc_predictions)
        
        expected_keys = [
            'epistemic_uncertainty', 'aleatoric_uncertainty', 'total_uncertainty',
            'mutual_information', 'prediction_variance', 'prediction_std'
        ]
        
        for key in expected_keys:
            self.assertIn(key, uncertainty)
            self.assertIsInstance(uncertainty[key], float)
            self.assertGreaterEqual(uncertainty[key], 0)
    
    def test_ensemble_uncertainty(self):
        """测试集成不确定性估计"""
        model_predictions = [
            np.array([0.8, 0.2]),
            np.array([0.6, 0.4]),
            np.array([0.7, 0.3])
        ]
        
        uncertainty = self.uncertainty_estimator.estimate_ensemble_uncertainty(model_predictions)
        
        expected_keys = [
            'ensemble_entropy', 'mean_pairwise_disagreement', 'max_pairwise_disagreement',
            'prediction_variance', 'class_consensus', 'class_entropy'
        ]
        
        for key in expected_keys:
            self.assertIn(key, uncertainty)
            self.assertIsInstance(uncertainty[key], float)
            self.assertGreaterEqual(uncertainty[key], 0)
    
    def test_uncertainty_decomposition(self):
        """测试不确定性分解"""
        predictions = [
            np.array([0.7, 0.3]),
            np.array([0.65, 0.35]),
            np.array([0.75, 0.25])
        ]
        
        # 测试互信息方法
        decomp_mi = self.uncertainty_estimator.decompose_uncertainty(predictions, 'mutual_information')
        self.assertIn('epistemic_uncertainty', decomp_mi)
        self.assertIn('aleatoric_uncertainty', decomp_mi)
        
        # 测试方差分解方法
        decomp_var = self.uncertainty_estimator.decompose_uncertainty(predictions, 'variance_decomposition')
        self.assertIn('epistemic_variance', decomp_var)
        self.assertIn('aleatoric_variance', decomp_var)

class TestEntropyBasedAmbiguityDetector(unittest.TestCase):
    """基于熵的歧义检测器测试"""
    
    def setUp(self):
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        config = EntropyDetectionConfig()
        config.analysis_data_path = self.temp_dir
        self.detector = EntropyBasedAmbiguityDetector(config)
    
    def tearDown(self):
        # 清理临时目录
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_extract_predictions(self):
        """测试预测提取"""
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.8},
            'hurtlex': {'verdict': 0, 'confidence': 0.7}
        }
        
        predictions = self.detector._extract_predictions(model_results)
        
        self.assertEqual(len(predictions), 2)
        for pred in predictions:
            self.assertEqual(len(pred), 2)
            self.assertAlmostEqual(np.sum(pred), 1.0, places=6)
    
    def test_ambiguity_detection_agreement(self):
        """测试一致预测的歧义检测"""
        text = "This is a test message"
        model_results = {
            'toxigen': {'verdict': 0, 'confidence': 0.9},
            'hurtlex': {'verdict': 0, 'confidence': 0.85}
        }
        
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, model_results)
        
        # 高置信度一致预测应该不被认为是歧义的
        self.assertIsInstance(is_ambiguous, bool)
        self.assertIsInstance(reasons, list)
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 1)
    
    def test_ambiguity_detection_disagreement(self):
        """测试分歧预测的歧义检测"""
        text = "This is a controversial message"
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.6},
            'hurtlex': {'verdict': 0, 'confidence': 0.7}
        }
        
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, model_results)
        
        # 分歧预测应该被认为是歧义的
        self.assertTrue(is_ambiguous)
        self.assertGreater(len(reasons), 0)
        self.assertGreater(score, 0.2)  # 应该有较高的歧义分数
    
    def test_ambiguity_detection_low_confidence(self):
        """测试低置信度的歧义检测"""
        text = "This is an uncertain message"
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.4},
            'hurtlex': {'verdict': 1, 'confidence': 0.45}
        }
        
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, model_results)
        
        # 低置信度预测应该被认为是歧义的
        self.assertTrue(is_ambiguous)
        self.assertIn('low_mean_confidence', ' '.join(reasons))
    
    def test_comprehensive_uncertainty_analysis(self):
        """测试综合不确定性分析"""
        model_predictions = [
            np.array([0.7, 0.3]),
            np.array([0.4, 0.6])
        ]
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.7},
            'hurtlex': {'verdict': 0, 'confidence': 0.6}
        }
        
        analysis = self.detector._comprehensive_uncertainty_analysis(model_predictions, model_results)
        
        expected_keys = [
            'individual_entropies', 'ensemble_entropy', 'predictive_entropy',
            'disagreement_metrics', 'uncertainty_estimates', 'confidence_stats', 'verdict_stats'
        ]
        
        for key in expected_keys:
            self.assertIn(key, analysis)
    
    def test_analysis_statistics(self):
        """测试分析统计信息"""
        # 添加一些分析历史
        for i in range(5):
            text = f"Test message {i}"
            model_results = {
                'toxigen': {'verdict': i % 2, 'confidence': 0.6 + i*0.1},
                'hurtlex': {'verdict': (i+1) % 2, 'confidence': 0.7 + i*0.05}
            }
            self.detector.detect_ambiguity(text, model_results)
        
        stats = self.detector.get_analysis_statistics()
        
        expected_keys = [
            'ambiguity_rate', 'total_analyzed', 'ambiguous_samples',
            'ambiguity_score_stats', 'top_ambiguity_reasons'
        ]
        
        for key in expected_keys:
            self.assertIn(key, stats)
    
    def test_save_load_analysis_data(self):
        """测试分析数据保存和加载"""
        # 添加一些分析数据
        text = "Test message"
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.6},
            'hurtlex': {'verdict': 0, 'confidence': 0.7}
        }
        self.detector.detect_ambiguity(text, model_results)
        
        # 保存数据
        filepath = os.path.join(self.temp_dir, "test_entropy_analysis.json")
        self.detector.save_analysis_data(filepath)
        
        # 检查文件是否存在
        self.assertTrue(os.path.exists(filepath))
        
        # 创建新的检测器并加载数据
        new_detector = EntropyBasedAmbiguityDetector(self.detector.config)
        new_detector.load_analysis_data(filepath)
        
        # 检查数据是否正确加载
        self.assertEqual(len(new_detector.analysis_history), 1)

def run_quick_test():
    """运行快速功能测试"""
    print("运行基于熵的歧义检测模块快速测试...")
    
    # 创建检测器
    detector = EntropyBasedAmbiguityDetector()
    
    # 测试一致预测
    print("\n1. 测试一致预测（低歧义）:")
    text1 = "This is a normal message"
    model_results1 = {
        'toxigen': {'verdict': 0, 'confidence': 0.9},
        'hurtlex': {'verdict': 0, 'confidence': 0.85}
    }
    
    is_ambiguous1, reasons1, score1 = detector.detect_ambiguity(text1, model_results1)
    print(f"文本: {text1}")
    print(f"是否歧义: {is_ambiguous1}")
    print(f"歧义分数: {score1:.4f}")
    print(f"歧义原因: {reasons1}")
    
    # 测试分歧预测
    print("\n2. 测试分歧预测（高歧义）:")
    text2 = "This is a controversial message"
    model_results2 = {
        'toxigen': {'verdict': 1, 'confidence': 0.6},
        'hurtlex': {'verdict': 0, 'confidence': 0.7}
    }
    
    is_ambiguous2, reasons2, score2 = detector.detect_ambiguity(text2, model_results2)
    print(f"文本: {text2}")
    print(f"是否歧义: {is_ambiguous2}")
    print(f"歧义分数: {score2:.4f}")
    print(f"歧义原因: {reasons2}")
    
    # 测试低置信度
    print("\n3. 测试低置信度（中等歧义）:")
    text3 = "This is an uncertain message"
    model_results3 = {
        'toxigen': {'verdict': 1, 'confidence': 0.4},
        'hurtlex': {'verdict': 1, 'confidence': 0.45}
    }
    
    is_ambiguous3, reasons3, score3 = detector.detect_ambiguity(text3, model_results3)
    print(f"文本: {text3}")
    print(f"是否歧义: {is_ambiguous3}")
    print(f"歧义分数: {score3:.4f}")
    print(f"歧义原因: {reasons3}")
    
    # 获取统计信息
    stats = detector.get_analysis_statistics()
    print(f"\n统计信息:")
    print(f"总分析样本: {stats.get('total_analyzed', 0)}")
    print(f"歧义样本: {stats.get('ambiguous_samples', 0)}")
    print(f"歧义率: {stats.get('ambiguity_rate', 0):.2%}")
    
    print("\n✅ 快速测试完成！")

if __name__ == '__main__':
    # 运行快速测试
    run_quick_test()
    
    # 运行完整单元测试
    print("\n" + "="*50)
    print("运行完整单元测试...")
    unittest.main(verbosity=2)
