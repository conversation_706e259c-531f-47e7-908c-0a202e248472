[{"model_dataset": "Mistral-7B-Instruct-v0.3_ImplicitHate", "model": "Mistral-7B-Instruct-v0.3", "dataset": "ImplicitHate", "f1_change": -0.009254471142980836, "accuracy_change": -0.0040000000000000036, "precision_change": -0.002290294875465171, "recall_change": -0.021052631578947323, "layer2_trigger_change": 0.03600000000000003, "confidence_change": -0.011961118704567841, "ambiguity_change": -0.3033856337849996, "current": {"model": "Mistral-7B-Instruct-v0.3", "dataset": "ImplicitHate", "num_samples": 1000, "accuracy": 0.689, "precision": 0.5691382765531062, "recall": 0.7473684210526316, "f1": 0.646188850967008, "layer2_trigger_rate": 0.927, "avg_confidence": 0.7279400008137121, "confidence_std": 0.11855990201890111, "avg_ambiguity": 0.37206533591197, "ambiguity_std": 0.07859760204276277, "decision_paths": {"Layer1_ToxiGen_High_Confidence_NonHate": 190, "Layer1_ToxiGen_High_Confidence_Hate": 306, "Layer1_ToxiGen_High_Confidence_Override": 398, "Layer1_Ambiguous": 76, "Layer1_Consensus_Hate": 23, "Layer1_Consensus_NonHate": 7}, "version": "current", "file": "logs\\twolayerdetectionsystem_Mistral-7B-Instruct-v0.3_ImplicitHate_log_3a3e086e_20250804130334.json"}, "previous": {"model": "Mistral-7B-Instruct-v0.3", "dataset": "ImplicitHate", "num_samples": 1000, "accuracy": 0.693, "precision": 0.5714285714285714, "recall": 0.7684210526315789, "f1": 0.6554433221099888, "layer2_trigger_rate": 0.891, "avg_confidence": 0.73990111951828, "confidence_std": 0.13018654034736715, "avg_ambiguity": 0.6754509696969696, "ambiguity_std": 0.1562704647268975, "decision_paths": {"Layer1_ToxiGen_High_Confidence_NonHate": 190, "Layer1_ToxiGen_High_Confidence_Hate": 306, "Layer1_ToxiGen_High_Confidence_Override": 398, "Layer1_Ambiguous": 76, "Layer1_Consensus_Hate": 23, "Layer1_Consensus_NonHate": 7}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_Mistral-7B-Instruct-v0.3_ImplicitHate_log_8ec1840e_20250802191423.json"}}, {"model_dataset": "gpt-3.5-turbo-0125_HateSpeechStormfront", "model": "gpt-3.5-turbo-0125", "dataset": "HateSpeechStormfront", "f1_change": -0.007119594031659515, "accuracy_change": -0.006276150627615107, "precision_change": -0.002089068825910867, "recall_change": -0.012552301255230103, "layer2_trigger_change": 0.05648535564853563, "confidence_change": -0.013354284630628266, "ambiguity_change": -0.244616875486112, "current": {"model": "gpt-3.5-turbo-0125", "dataset": "HateSpeechStormfront", "num_samples": 478, "accuracy": 0.8368200836820083, "precision": 0.8259109311740891, "recall": 0.8535564853556485, "f1": 0.8395061728395061, "layer2_trigger_rate": 0.9330543933054394, "avg_confidence": 0.7582644397125401, "confidence_std": 0.10728017306907653, "avg_ambiguity": 0.3900023864333041, "ambiguity_std": 0.09177951926194292, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 163, "Layer1_ToxiGen_High_Confidence_Override": 154, "Layer1_ToxiGen_High_Confidence_NonHate": 127, "Layer1_Consensus_Hate": 17, "Layer1_Ambiguous": 16, "Layer1_Consensus_NonHate": 1}, "version": "current", "file": "logs\\twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechStormfront_log_c6b2440e_20250804122721.json"}, "previous": {"model": "gpt-3.5-turbo-0125", "dataset": "HateSpeechStormfront", "num_samples": 478, "accuracy": 0.8430962343096234, "precision": 0.828, "recall": 0.8661087866108786, "f1": 0.8466257668711656, "layer2_trigger_rate": 0.8765690376569037, "avg_confidence": 0.7716187243431684, "confidence_std": 0.10886205788894489, "avg_ambiguity": 0.6346192619194161, "ambiguity_std": 0.12895397801762307, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 163, "Layer1_ToxiGen_High_Confidence_Override": 154, "Layer1_ToxiGen_High_Confidence_NonHate": 127, "Layer1_Consensus_Hate": 17, "Layer1_Ambiguous": 16, "Layer1_Consensus_NonHate": 1}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechStormfront_log_30082e77_20250802174702.json"}}, {"model_dataset": "gpt-3.5-turbo-0125_ImplicitHate", "model": "gpt-3.5-turbo-0125", "dataset": "ImplicitHate", "f1_change": -0.005700139382970315, "accuracy_change": -0.0020000000000000018, "precision_change": 0.00035629838370099254, "recall_change": -0.015789473684210464, "layer2_trigger_change": 0.03600000000000003, "confidence_change": -0.010911118704567957, "ambiguity_change": -0.3033856337849996, "current": {"model": "gpt-3.5-turbo-0125", "dataset": "ImplicitHate", "num_samples": 1000, "accuracy": 0.736, "precision": 0.6178861788617886, "recall": 0.8, "f1": 0.6972477064220184, "layer2_trigger_rate": 0.927, "avg_confidence": 0.7284400008137121, "confidence_std": 0.11799686591565776, "avg_ambiguity": 0.37206533591197, "ambiguity_std": 0.07859760204276277, "decision_paths": {"Layer1_ToxiGen_High_Confidence_NonHate": 190, "Layer1_ToxiGen_High_Confidence_Hate": 306, "Layer1_ToxiGen_High_Confidence_Override": 398, "Layer1_Ambiguous": 76, "Layer1_Consensus_Hate": 23, "Layer1_Consensus_NonHate": 7}, "version": "current", "file": "logs\\twolayerdetectionsystem_gpt-3.5-turbo-0125_ImplicitHate_log_dacad14a_20250804124052.json"}, "previous": {"model": "gpt-3.5-turbo-0125", "dataset": "ImplicitHate", "num_samples": 1000, "accuracy": 0.738, "precision": 0.6175298804780877, "recall": 0.8157894736842105, "f1": 0.7029478458049887, "layer2_trigger_rate": 0.891, "avg_confidence": 0.73935111951828, "confidence_std": 0.1307081635518102, "avg_ambiguity": 0.6754509696969696, "ambiguity_std": 0.1562704647268975, "decision_paths": {"Layer1_ToxiGen_High_Confidence_NonHate": 190, "Layer1_ToxiGen_High_Confidence_Hate": 306, "Layer1_ToxiGen_High_Confidence_Override": 398, "Layer1_Ambiguous": 76, "Layer1_Consensus_Hate": 23, "Layer1_Consensus_NonHate": 7}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_gpt-3.5-turbo-0125_ImplicitHate_log_11c572f5_20250802180646.json"}}, {"model_dataset": "Mistral-7B-Instruct-v0.3_HateSpeechStormfront", "model": "Mistral-7B-Instruct-v0.3", "dataset": "HateSpeechStormfront", "f1_change": -0.005269841269841313, "accuracy_change": -0.004184100418409997, "precision_change": 0.0007662835249041544, "recall_change": -0.012552301255230103, "layer2_trigger_change": 0.05648535564853563, "confidence_change": -0.011576041952803817, "ambiguity_change": -0.244616875486112, "current": {"model": "Mistral-7B-Instruct-v0.3", "dataset": "HateSpeechStormfront", "num_samples": 478, "accuracy": 0.8284518828451883, "precision": 0.8007662835249042, "recall": 0.8744769874476988, "f1": 0.836, "layer2_trigger_rate": 0.9330543933054394, "avg_confidence": 0.7449799208840883, "confidence_std": 0.11678593830956371, "avg_ambiguity": 0.3900023864333041, "ambiguity_std": 0.09177951926194292, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 163, "Layer1_ToxiGen_High_Confidence_Override": 154, "Layer1_ToxiGen_High_Confidence_NonHate": 127, "Layer1_Consensus_Hate": 17, "Layer1_Ambiguous": 16, "Layer1_Consensus_NonHate": 1}, "version": "current", "file": "logs\\twolayerdetectionsystem_Mistral-7B-Instruct-v0.3_HateSpeechStormfront_log_ea015dbb_20250804153431.json"}, "previous": {"model": "Mistral-7B-Instruct-v0.3", "dataset": "HateSpeechStormfront", "num_samples": 478, "accuracy": 0.8326359832635983, "precision": 0.8, "recall": 0.8870292887029289, "f1": 0.8412698412698413, "layer2_trigger_rate": 0.8765690376569037, "avg_confidence": 0.7565559628368921, "confidence_std": 0.12138160996270843, "avg_ambiguity": 0.6346192619194161, "ambiguity_std": 0.12895397801762307, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 163, "Layer1_ToxiGen_High_Confidence_Override": 154, "Layer1_ToxiGen_High_Confidence_NonHate": 127, "Layer1_Consensus_Hate": 17, "Layer1_Ambiguous": 16, "Layer1_Consensus_NonHate": 1}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_Mistral-7B-Instruct-v0.3_HateSpeechStormfront_log_0048f1eb_20250802214315.json"}}, {"model_dataset": "Qwen2-7B-Instruct_ImplicitHate", "model": "Qwen2-7B-Instruct", "dataset": "ImplicitHate", "f1_change": -0.004609869174315384, "accuracy_change": 0.0020000000000000018, "precision_change": 0.004535902921075974, "recall_change": -0.021052631578947323, "layer2_trigger_change": 0.03600000000000003, "confidence_change": -0.011461118704568007, "ambiguity_change": -0.3033856337849996, "current": {"model": "Qwen2-7B-Instruct", "dataset": "ImplicitHate", "num_samples": 1000, "accuracy": 0.697, "precision": 0.5762376237623762, "recall": 0.7657894736842106, "f1": 0.6576271186440678, "layer2_trigger_rate": 0.927, "avg_confidence": 0.7359400008137122, "confidence_std": 0.11300624033084207, "avg_ambiguity": 0.37206533591197, "ambiguity_std": 0.07859760204276277, "decision_paths": {"Layer1_ToxiGen_High_Confidence_NonHate": 190, "Layer1_ToxiGen_High_Confidence_Hate": 306, "Layer1_ToxiGen_High_Confidence_Override": 398, "Layer1_Ambiguous": 76, "Layer1_Consensus_Hate": 23, "Layer1_Consensus_NonHate": 7}, "version": "current", "file": "logs\\twolayerdetectionsystem_Qwen2-7B-Instruct_ImplicitHate_log_3d8cf97d_20250804184812.json"}, "previous": {"model": "Qwen2-7B-Instruct", "dataset": "ImplicitHate", "num_samples": 1000, "accuracy": 0.695, "precision": 0.5717017208413002, "recall": 0.7868421052631579, "f1": 0.6622369878183831, "layer2_trigger_rate": 0.891, "avg_confidence": 0.7474011195182803, "confidence_std": 0.12429307500759025, "avg_ambiguity": 0.6754509696969696, "ambiguity_std": 0.1562704647268975, "decision_paths": {"Layer1_ToxiGen_High_Confidence_NonHate": 190, "Layer1_ToxiGen_High_Confidence_Hate": 306, "Layer1_ToxiGen_High_Confidence_Override": 398, "Layer1_Ambiguous": 76, "Layer1_Consensus_Hate": 23, "Layer1_Consensus_NonHate": 7}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_Qwen2-7B-Instruct_ImplicitHate_log_dfcf4a1e_20250802223721.json"}}, {"model_dataset": "Qwen2-7B-Instruct_HateSpeechStormfront", "model": "Qwen2-7B-Instruct", "dataset": "HateSpeechStormfront", "f1_change": -0.0024473040183152772, "accuracy_change": -0.0020920502092049986, "precision_change": -0.0007032101543545943, "recall_change": -0.004184100418409997, "layer2_trigger_change": 0.05648535564853563, "confidence_change": -0.010634619358661723, "ambiguity_change": -0.244616875486112, "current": {"model": "Qwen2-7B-Instruct", "dataset": "HateSpeechStormfront", "num_samples": 478, "accuracy": 0.8305439330543933, "precision": 0.8319327731092437, "recall": 0.8284518828451883, "f1": 0.8301886792452831, "layer2_trigger_rate": 0.9330543933054394, "avg_confidence": 0.7594150673276029, "confidence_std": 0.10558756956059796, "avg_ambiguity": 0.3900023864333041, "ambiguity_std": 0.09177951926194292, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 163, "Layer1_ToxiGen_High_Confidence_Override": 154, "Layer1_ToxiGen_High_Confidence_NonHate": 127, "Layer1_Consensus_Hate": 17, "Layer1_Ambiguous": 16, "Layer1_Consensus_NonHate": 1}, "version": "current", "file": "logs\\twolayerdetectionsystem_Qwen2-7B-Instruct_HateSpeechStormfront_log_501df5d9_20250804210630.json"}, "previous": {"model": "Qwen2-7B-Instruct", "dataset": "HateSpeechStormfront", "num_samples": 478, "accuracy": 0.8326359832635983, "precision": 0.8326359832635983, "recall": 0.8326359832635983, "f1": 0.8326359832635983, "layer2_trigger_rate": 0.8765690376569037, "avg_confidence": 0.7700496866862646, "confidence_std": 0.11047164277582824, "avg_ambiguity": 0.6346192619194161, "ambiguity_std": 0.12895397801762307, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 163, "Layer1_ToxiGen_High_Confidence_Override": 154, "Layer1_ToxiGen_High_Confidence_NonHate": 127, "Layer1_Consensus_Hate": 17, "Layer1_Ambiguous": 16, "Layer1_Consensus_NonHate": 1}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_Qwen2-7B-Instruct_HateSpeechStormfront_log_128ae10e_20250803005404.json"}}, {"model_dataset": "gpt-3.5-turbo-0125_HateSpeechOffensive", "model": "gpt-3.5-turbo-0125", "dataset": "HateSpeechOffensive", "f1_change": -0.00019193340557788208, "accuracy_change": 0.0, "precision_change": 0.0032013034697998544, "recall_change": -0.0036014405762304635, "layer2_trigger_change": 0.07400000000000007, "confidence_change": -0.016062111309958715, "ambiguity_change": -0.21059281769965982, "current": {"model": "gpt-3.5-turbo-0125", "dataset": "HateSpeechOffensive", "num_samples": 1000, "accuracy": 0.911, "precision": 0.9471153846153846, "recall": 0.9459783913565426, "f1": 0.9465465465465466, "layer2_trigger_rate": 0.915, "avg_confidence": 0.780113144143628, "confidence_std": 0.08643751908728306, "avg_ambiguity": 0.36102580093087827, "ambiguity_std": 0.07311988048161507, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 745, "Layer1_ToxiGen_High_Confidence_Override": 146, "Layer1_ToxiGen_High_Confidence_NonHate": 73, "Layer1_Consensus_Hate": 8, "Layer1_Ambiguous": 28}, "version": "current", "file": "logs\\twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechOffensive_log_d6b6c49b_20250804123812.json"}, "previous": {"model": "gpt-3.5-turbo-0125", "dataset": "HateSpeechOffensive", "num_samples": 1000, "accuracy": 0.911, "precision": 0.9439140811455847, "recall": 0.9495798319327731, "f1": 0.9467384799521245, "layer2_trigger_rate": 0.841, "avg_confidence": 0.7961752554535867, "confidence_std": 0.09802119732213958, "avg_ambiguity": 0.5716186186305381, "ambiguity_std": 0.1479201810246353, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 745, "Layer1_ToxiGen_High_Confidence_Override": 146, "Layer1_ToxiGen_High_Confidence_NonHate": 73, "Layer1_Consensus_Hate": 8, "Layer1_Ambiguous": 28}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechOffensive_log_fc9adb7b_20250802180617.json"}}, {"model_dataset": "Qwen2-7B-Instruct_HateSpeechOffensive", "model": "Qwen2-7B-Instruct", "dataset": "HateSpeechOffensive", "f1_change": -7.886020713954167e-05, "accuracy_change": 0.0, "precision_change": 0.0010729883688060937, "recall_change": -0.0012004801920768582, "layer2_trigger_change": 0.07400000000000007, "confidence_change": -0.01576211130995875, "ambiguity_change": -0.21059281769965982, "current": {"model": "Qwen2-7B-Instruct", "dataset": "HateSpeechOffensive", "num_samples": 1000, "accuracy": 0.892, "precision": 0.9415347137637028, "recall": 0.9279711884753902, "f1": 0.9347037484885127, "layer2_trigger_rate": 0.915, "avg_confidence": 0.7736631441436279, "confidence_std": 0.09306342872158334, "avg_ambiguity": 0.36102580093087827, "ambiguity_std": 0.07311988048161507, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 745, "Layer1_ToxiGen_High_Confidence_Override": 146, "Layer1_ToxiGen_High_Confidence_NonHate": 73, "Layer1_Consensus_Hate": 8, "Layer1_Ambiguous": 28}, "version": "current", "file": "logs\\twolayerdetectionsystem_Qwen2-7B-Instruct_HateSpeechOffensive_log_df4e3705_20250804200209.json"}, "previous": {"model": "Qwen2-7B-Instruct", "dataset": "HateSpeechOffensive", "num_samples": 1000, "accuracy": 0.892, "precision": 0.9404617253948967, "recall": 0.929171668667467, "f1": 0.9347826086956522, "layer2_trigger_rate": 0.841, "avg_confidence": 0.7894252554535867, "confidence_std": 0.10615299606270773, "avg_ambiguity": 0.5716186186305381, "ambiguity_std": 0.1479201810246353, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 745, "Layer1_ToxiGen_High_Confidence_Override": 146, "Layer1_ToxiGen_High_Confidence_NonHate": 73, "Layer1_Consensus_Hate": 8, "Layer1_Ambiguous": 28}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_Qwen2-7B-Instruct_HateSpeechOffensive_log_c0cb6fa6_20250802235346.json"}}, {"model_dataset": "Mistral-7B-Instruct-v0.3_HateSpeechOffensive", "model": "Mistral-7B-Instruct-v0.3", "dataset": "HateSpeechOffensive", "f1_change": 0.0004627484039724239, "accuracy_change": 0.0010000000000000009, "precision_change": 0.0031586831222116807, "recall_change": -0.0024009603841537164, "layer2_trigger_change": 0.07400000000000007, "confidence_change": -0.016612111309958766, "ambiguity_change": -0.21059281769965982, "current": {"model": "Mistral-7B-Instruct-v0.3", "dataset": "HateSpeechOffensive", "num_samples": 1000, "accuracy": 0.927, "precision": 0.9439252336448598, "recall": 0.9699879951980792, "f1": 0.9567791592658378, "layer2_trigger_rate": 0.915, "avg_confidence": 0.7787131441436279, "confidence_std": 0.08702759050764637, "avg_ambiguity": 0.36102580093087827, "ambiguity_std": 0.07311988048161507, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 745, "Layer1_ToxiGen_High_Confidence_Override": 146, "Layer1_ToxiGen_High_Confidence_NonHate": 73, "Layer1_Consensus_Hate": 8, "Layer1_Ambiguous": 28}, "version": "current", "file": "logs\\twolayerdetectionsystem_Mistral-7B-Instruct-v0.3_HateSpeechOffensive_log_08cd9a13_20250804142716.json"}, "previous": {"model": "Mistral-7B-Instruct-v0.3", "dataset": "HateSpeechOffensive", "num_samples": 1000, "accuracy": 0.926, "precision": 0.9407665505226481, "recall": 0.9723889555822329, "f1": 0.9563164108618654, "layer2_trigger_rate": 0.841, "avg_confidence": 0.7953252554535867, "confidence_std": 0.0982009702535414, "avg_ambiguity": 0.5716186186305381, "ambiguity_std": 0.1479201810246353, "decision_paths": {"Layer1_ToxiGen_High_Confidence_Hate": 745, "Layer1_ToxiGen_High_Confidence_Override": 146, "Layer1_ToxiGen_High_Confidence_NonHate": 73, "Layer1_Consensus_Hate": 8, "Layer1_Ambiguous": 28}, "version": "previous", "file": "previous_log\\twolayerdetectionsystem_Mistral-7B-Instruct-v0.3_HateSpeechOffensive_log_b09a7a5b_20250802203736.json"}}]