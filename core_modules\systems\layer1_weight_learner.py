#!/usr/bin/env python3
"""
第一层权重学习器
实现轻量级的动态权重学习，专门用于ToxiGen和HurtLex的权重调整
"""

import time
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class Layer1WeightConfig:
    """第一层权重学习配置"""
    # 学习参数
    learning_rate: float = 0.01  # 学习率
    min_weight: float = 0.05     # 最小权重
    max_weight: float = 0.95     # 最大权重
    
    # 性能窗口
    performance_window: int = 50  # 性能历史窗口大小
    min_samples_for_update: int = 5  # 开始更新权重的最小样本数
    
    # 学习策略
    enable_adaptive_lr: bool = True  # 启用自适应学习率
    lr_decay_factor: float = 0.95    # 学习率衰减因子
    lr_increase_factor: float = 1.05 # 学习率增长因子
    
    # 权重更新策略
    reward_correct: float = 0.5      # 正确预测的奖励
    penalty_incorrect: float = -0.5  # 错误预测的惩罚
    
    # 日志配置
    log_updates: bool = True         # 是否记录权重更新
    update_log_interval: int = 10    # 每N次更新记录一次日志

class Layer1WeightLearner:
    """第一层权重学习器 - 轻量级实现"""
    
    def __init__(self, config: Layer1WeightConfig = None):
        """
        初始化权重学习器
        
        Args:
            config: 权重学习配置
        """
        self.config = config or Layer1WeightConfig()
        
        # 当前权重 (ToxiGen, HurtLex) - 平均分配
        self.toxigen_weight = 0.5
        self.hurtlex_weight = 0.5
        
        # 学习状态
        self.current_lr = self.config.learning_rate
        self.update_count = 0
        
        # 性能历史
        self.performance_history = {
            'toxigen': {'correct': [], 'total': []},
            'hurtlex': {'correct': [], 'total': []}
        }
        
        # 权重历史（用于分析）
        self.weight_history = []
        
        logger.info(f"✅ Layer1WeightLearner初始化完成")
        logger.info(f"   初始权重: ToxiGen={self.toxigen_weight}, HurtLex={self.hurtlex_weight}")
        logger.info(f"   学习率: {self.current_lr}")
    
    def get_current_weights(self) -> Tuple[float, float]:
        """获取当前权重"""
        return self.toxigen_weight, self.hurtlex_weight
    
    def update_weights(self, toxigen_result: Dict, hurtlex_result: Dict, true_label: int) -> bool:
        """
        基于预测结果更新权重
        
        Args:
            toxigen_result: ToxiGen预测结果
            hurtlex_result: HurtLex预测结果  
            true_label: 真实标签 (0 或 1)
            
        Returns:
            bool: 是否进行了权重更新
        """
        if true_label not in [0, 1]:
            return False
            
        # 提取预测结果
        toxigen_verdict = toxigen_result.get('verdict', -1)
        hurtlex_verdict = hurtlex_result.get('verdict', -1)
        
        if toxigen_verdict not in [0, 1] or hurtlex_verdict not in [0, 1]:
            return False
        
        # 计算准确性
        toxigen_correct = (toxigen_verdict == true_label)
        hurtlex_correct = (hurtlex_verdict == true_label)
        
        # 更新性能历史
        self._update_performance_history('toxigen', toxigen_correct)
        self._update_performance_history('hurtlex', hurtlex_correct)

        # 递增更新计数
        self.update_count += 1

        # 检查是否有足够样本进行权重更新
        if self.update_count < self.config.min_samples_for_update:
            if self.config.log_updates:
                logger.info(f"📊 样本收集中 ({self.update_count}/{self.config.min_samples_for_update})")
                logger.info(f"   ToxiGen: {'✓' if toxigen_correct else '✗'}, HurtLex: {'✓' if hurtlex_correct else '✗'}")
            return False
        
        # 计算权重调整
        weight_adjustment = self._calculate_weight_adjustment(toxigen_correct, hurtlex_correct)

        # 自适应学习率调整（无论是否更新权重都要调整）
        if self.config.enable_adaptive_lr:
            self._adjust_learning_rate(toxigen_correct, hurtlex_correct)

        if weight_adjustment != 0:
            # 更新权重
            old_toxigen_weight = self.toxigen_weight
            old_hurtlex_weight = self.hurtlex_weight
            
            self.toxigen_weight += weight_adjustment
            self.hurtlex_weight -= weight_adjustment
            
            # 确保权重在合理范围内
            self.toxigen_weight = max(self.config.min_weight, 
                                    min(self.config.max_weight, self.toxigen_weight))
            self.hurtlex_weight = max(self.config.min_weight, 
                                    min(self.config.max_weight, self.hurtlex_weight))
            
            # 归一化权重
            total_weight = self.toxigen_weight + self.hurtlex_weight
            self.toxigen_weight /= total_weight
            self.hurtlex_weight /= total_weight
            
            # 记录权重历史
            self.weight_history.append({
                'update_count': self.update_count,
                'toxigen_weight': self.toxigen_weight,
                'hurtlex_weight': self.hurtlex_weight,
                'toxigen_correct': toxigen_correct,
                'hurtlex_correct': hurtlex_correct,
                'weight_adjustment': weight_adjustment,
                'learning_rate': self.current_lr,
                'timestamp': time.time()
            })
            
            # 学习率调整已在前面执行
            
            # 记录日志
            if (self.config.log_updates and 
                self.update_count % self.config.update_log_interval == 0):
                logger.info(f"🔄 权重更新 #{self.update_count}")
                logger.info(f"   ToxiGen: {old_toxigen_weight:.3f} → {self.toxigen_weight:.3f}")
                logger.info(f"   HurtLex: {old_hurtlex_weight:.3f} → {self.hurtlex_weight:.3f}")
                logger.info(f"   学习率: {self.current_lr:.4f}")
                logger.info(f"   准确性: ToxiGen={toxigen_correct}, HurtLex={hurtlex_correct}")
            
            self.update_count += 1
            return True
        
        self.update_count += 1
        return False
    
    def _update_performance_history(self, agent_name: str, is_correct: bool):
        """更新性能历史"""
        self.performance_history[agent_name]['correct'].append(1 if is_correct else 0)
        self.performance_history[agent_name]['total'].append(1)
        
        # 保持窗口大小
        if len(self.performance_history[agent_name]['correct']) > self.config.performance_window:
            self.performance_history[agent_name]['correct'] = \
                self.performance_history[agent_name]['correct'][-self.config.performance_window:]
            self.performance_history[agent_name]['total'] = \
                self.performance_history[agent_name]['total'][-self.config.performance_window:]
    
    def _calculate_weight_adjustment(self, toxigen_correct: bool, hurtlex_correct: bool) -> float:
        """
        计算权重调整量
        使用对称奖励/惩罚机制
        """
        # 基础调整量
        toxigen_adjustment = (self.config.reward_correct if toxigen_correct 
                            else self.config.penalty_incorrect)
        hurtlex_adjustment = (self.config.reward_correct if hurtlex_correct 
                            else self.config.penalty_incorrect)
        
        # 计算相对调整（ToxiGen权重的变化）
        relative_adjustment = (toxigen_adjustment - hurtlex_adjustment) * self.current_lr
        
        return relative_adjustment
    
    def _adjust_learning_rate(self, toxigen_correct: bool, hurtlex_correct: bool):
        """自适应学习率调整"""
        # 如果两个智能体都正确或都错误，说明当前权重可能合适，降低学习率
        if toxigen_correct == hurtlex_correct:
            self.current_lr *= self.config.lr_decay_factor
        else:
            # 如果智能体表现不同，可能需要调整权重，增加学习率
            self.current_lr *= self.config.lr_increase_factor
        
        # 限制学习率范围
        self.current_lr = max(0.001, min(0.1, self.current_lr))
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            'current_weights': {
                'toxigen': self.toxigen_weight,
                'hurtlex': self.hurtlex_weight
            },
            'update_count': self.update_count,
            'current_learning_rate': self.current_lr,
            'weight_history_length': len(self.weight_history)
        }
        
        # 计算最近性能
        for agent_name in ['toxigen', 'hurtlex']:
            correct_list = self.performance_history[agent_name]['correct']
            if correct_list:
                recent_accuracy = sum(correct_list[-20:]) / min(len(correct_list), 20)
                overall_accuracy = sum(correct_list) / len(correct_list)
                stats[f'{agent_name}_recent_accuracy'] = recent_accuracy
                stats[f'{agent_name}_overall_accuracy'] = overall_accuracy
            else:
                stats[f'{agent_name}_recent_accuracy'] = 0.0
                stats[f'{agent_name}_overall_accuracy'] = 0.0
        
        return stats
    
    def reset_weights(self, toxigen_weight: float = 0.5, hurtlex_weight: float = 0.5):
        """重置权重到指定值"""
        total = toxigen_weight + hurtlex_weight
        self.toxigen_weight = toxigen_weight / total
        self.hurtlex_weight = hurtlex_weight / total
        
        logger.info(f"🔄 权重已重置: ToxiGen={self.toxigen_weight:.3f}, HurtLex={self.hurtlex_weight:.3f}")
    
    def save_learning_history(self, filepath: str):
        """保存学习历史到文件"""
        history_data = {
            'config': asdict(self.config),
            'final_weights': {
                'toxigen': self.toxigen_weight,
                'hurtlex': self.hurtlex_weight
            },
            'performance_stats': self.get_performance_stats(),
            'weight_history': self.weight_history
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📁 学习历史已保存到: {filepath}")
