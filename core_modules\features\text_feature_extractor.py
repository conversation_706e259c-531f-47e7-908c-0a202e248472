#!/usr/bin/env python3
"""
文本特征提取器
Text Feature Extractor

提取可靠的文本特征用于仇恨言论检测
包括词汇特征、语义特征、结构特征和模型一致性特征

作者: AI Assistant
日期: 2025-08-02
版本: 1.0
"""

import re
import string
import numpy as np
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging
from collections import Counter
import math

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FeatureConfig:
    """特征提取配置"""
    # 词汇特征配置
    enable_lexical_features: bool = True
    min_word_length: int = 2
    max_word_length: int = 20
    
    # 语义特征配置
    enable_semantic_features: bool = True
    sentiment_threshold: float = 0.5
    
    # 结构特征配置
    enable_structural_features: bool = True
    
    # 模型一致性特征配置
    enable_consistency_features: bool = True

class TextFeatureExtractor:
    """文本特征提取器"""
    
    def __init__(self, config: FeatureConfig = None):
        """
        初始化特征提取器
        
        Args:
            config: 特征提取配置
        """
        self.config = config or FeatureConfig()
        
        # 预定义的特征词汇集合
        self._initialize_feature_vocabularies()
        
        logger.info("文本特征提取器初始化完成")
    
    def _initialize_feature_vocabularies(self):
        """初始化特征词汇集合"""
        # 否定词
        self.negation_words = {
            'not', 'no', 'never', 'nothing', 'nobody', 'nowhere', 'neither', 'nor',
            'none', 'hardly', 'scarcely', 'barely', 'seldom', 'rarely', "n't",
            'without', 'lack', 'absent', 'missing', 'void', 'empty'
        }
        
        # 强化词
        self.intensifiers = {
            'very', 'extremely', 'really', 'quite', 'rather', 'pretty', 'fairly',
            'absolutely', 'completely', 'totally', 'entirely', 'utterly', 'highly',
            'deeply', 'strongly', 'seriously', 'incredibly', 'amazingly', 'super'
        }
        
        # 情绪词汇
        self.emotion_words = {
            'positive': {
                'love', 'like', 'enjoy', 'happy', 'joy', 'pleased', 'glad', 'excited',
                'wonderful', 'amazing', 'great', 'excellent', 'fantastic', 'awesome'
            },
            'negative': {
                'hate', 'dislike', 'angry', 'mad', 'furious', 'disgusted', 'annoyed',
                'terrible', 'awful', 'horrible', 'disgusting', 'pathetic', 'stupid'
            }
        }
        
        # 间接指代词
        self.indirect_references = {
            'some people', 'these people', 'those people', 'they', 'them', 'their',
            'certain people', 'such people', 'people like that', 'that kind',
            'this type', 'those types', 'certain groups', 'some groups'
        }
        
        # 文化暗示词组
        self.cultural_hints = [
            ('urban', 'culture'), ('inner', 'city'), ('welfare', 'queen'),
            ('traditional', 'values'), ('real', 'american'), ('law', 'order'),
            ('family', 'values'), ('political', 'correctness'), ('reverse', 'racism')
        ]
    
    def extract_features(self, text: str, model_results: Optional[Dict] = None) -> Dict[str, Any]:
        """
        提取文本的所有特征
        
        Args:
            text: 输入文本
            model_results: 模型预测结果（用于一致性特征）
            
        Returns:
            特征字典
        """
        features = {}
        
        # 基础文本预处理
        processed_text = self._preprocess_text(text)
        words = self._tokenize(processed_text)
        
        # 提取各类特征
        if self.config.enable_lexical_features:
            features.update(self._extract_lexical_features(text, words))
        
        if self.config.enable_semantic_features:
            features.update(self._extract_semantic_features(text, words))
        
        if self.config.enable_structural_features:
            features.update(self._extract_structural_features(text, words))
        
        if self.config.enable_consistency_features and model_results:
            features.update(self._extract_consistency_features(model_results))
        
        return features
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 转换为小写
        text = text.lower()
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def _tokenize(self, text: str) -> List[str]:
        """分词"""
        # 简单的基于空格和标点的分词
        words = re.findall(r'\b[a-zA-Z]+\b', text)
        # 过滤长度
        words = [w for w in words if self.config.min_word_length <= len(w) <= self.config.max_word_length]
        return words
    
    def _extract_lexical_features(self, text: str, words: List[str]) -> Dict[str, Any]:
        """提取词汇特征"""
        features = {}
        
        # 基础统计
        features['word_count'] = len(words)
        features['unique_word_count'] = len(set(words))
        features['char_count'] = len(text)
        features['avg_word_length'] = np.mean([len(w) for w in words]) if words else 0
        
        # 词汇多样性
        features['lexical_diversity'] = features['unique_word_count'] / features['word_count'] if features['word_count'] > 0 else 0
        
        # 大写字母比例
        uppercase_chars = sum(1 for c in text if c.isupper())
        features['uppercase_ratio'] = uppercase_chars / len(text) if len(text) > 0 else 0
        
        # 否定词计数
        negation_count = sum(1 for word in words if word in self.negation_words)
        features['negation_count'] = negation_count
        features['negation_ratio'] = negation_count / len(words) if words else 0
        
        # 强化词计数
        intensifier_count = sum(1 for word in words if word in self.intensifiers)
        features['intensifier_count'] = intensifier_count
        features['intensifier_ratio'] = intensifier_count / len(words) if words else 0
        
        # 情绪词计数
        positive_count = sum(1 for word in words if word in self.emotion_words['positive'])
        negative_count = sum(1 for word in words if word in self.emotion_words['negative'])
        features['positive_emotion_count'] = positive_count
        features['negative_emotion_count'] = negative_count
        features['emotion_polarity'] = (positive_count - negative_count) / len(words) if words else 0
        
        return features
    
    def _extract_semantic_features(self, text: str, words: List[str]) -> Dict[str, Any]:
        """提取语义特征"""
        features = {}
        
        # 间接指代检测
        text_lower = text.lower()
        indirect_count = sum(1 for ref in self.indirect_references if ref in text_lower)
        features['indirect_reference_count'] = indirect_count
        features['has_indirect_reference'] = indirect_count > 0
        
        # 文化暗示检测
        cultural_hint_count = 0
        for hint_pair in self.cultural_hints:
            if all(word in text_lower for word in hint_pair):
                cultural_hint_count += 1
        features['cultural_hint_count'] = cultural_hint_count
        features['has_cultural_hint'] = cultural_hint_count > 0
        
        # 泛化表达检测（"总是"、"从不"等）
        generalization_words = ['always', 'never', 'all', 'every', 'none', 'everyone', 'nobody']
        generalization_count = sum(1 for word in words if word in generalization_words)
        features['generalization_count'] = generalization_count
        features['has_generalization'] = generalization_count > 0
        
        # 对比表达检测（"我们"vs"他们"）
        us_words = ['we', 'us', 'our', 'ours']
        them_words = ['they', 'them', 'their', 'theirs']
        us_count = sum(1 for word in words if word in us_words)
        them_count = sum(1 for word in words if word in them_words)
        features['us_them_contrast'] = abs(us_count - them_count)
        features['has_us_them_contrast'] = us_count > 0 and them_count > 0
        
        return features
    
    def _extract_structural_features(self, text: str, words: List[str]) -> Dict[str, Any]:
        """提取结构特征"""
        features = {}
        
        # 句子长度
        features['sentence_length'] = len(text)
        
        # 标点符号统计
        punctuation_count = sum(1 for c in text if c in string.punctuation)
        features['punctuation_count'] = punctuation_count
        features['punctuation_density'] = punctuation_count / len(text) if len(text) > 0 else 0
        
        # 特殊字符统计
        exclamation_count = text.count('!')
        question_count = text.count('?')
        features['exclamation_count'] = exclamation_count
        features['question_count'] = question_count
        features['has_multiple_exclamations'] = exclamation_count > 1
        
        # 重复字符检测
        repeated_chars = len(re.findall(r'(.)\1{2,}', text))  # 连续3个或更多相同字符
        features['repeated_char_count'] = repeated_chars
        features['has_repeated_chars'] = repeated_chars > 0
        
        # 全大写单词
        uppercase_words = sum(1 for word in words if word.isupper() and len(word) > 1)
        features['uppercase_word_count'] = uppercase_words
        features['has_uppercase_words'] = uppercase_words > 0
        
        return features
    
    def _extract_consistency_features(self, model_results: Dict) -> Dict[str, Any]:
        """提取模型一致性特征"""
        features = {}
        
        # 收集所有模型的预测结果
        verdicts = []
        confidences = []
        
        for model_name, result in model_results.items():
            if isinstance(result, dict) and 'verdict' in result:
                verdicts.append(result['verdict'])
                confidences.append(result.get('confidence', 0.5))
        
        if not verdicts:
            return features
        
        # 预测分歧度
        unique_verdicts = len(set(verdicts))
        features['prediction_disagreement'] = unique_verdicts > 1
        features['disagreement_count'] = unique_verdicts
        
        # 置信度统计
        features['confidence_mean'] = np.mean(confidences)
        features['confidence_std'] = np.std(confidences)
        features['confidence_min'] = np.min(confidences)
        features['confidence_max'] = np.max(confidences)
        features['confidence_range'] = features['confidence_max'] - features['confidence_min']
        
        # 预测稳定性
        verdict_counts = Counter(verdicts)
        most_common_count = verdict_counts.most_common(1)[0][1]
        features['prediction_stability'] = most_common_count / len(verdicts)
        
        # 低置信度预测数量
        low_confidence_count = sum(1 for conf in confidences if conf < 0.6)
        features['low_confidence_count'] = low_confidence_count
        features['has_low_confidence'] = low_confidence_count > 0
        
        return features
    
    def get_feature_importance_scores(self, features: Dict[str, Any]) -> Dict[str, float]:
        """
        计算特征重要性分数
        基于经验规则为不同特征分配重要性权重
        """
        importance_scores = {}
        
        # 高重要性特征
        high_importance = [
            'negative_emotion_count', 'has_indirect_reference', 'has_cultural_hint',
            'prediction_disagreement', 'confidence_range', 'has_generalization'
        ]
        
        # 中等重要性特征
        medium_importance = [
            'intensifier_count', 'negation_count', 'has_us_them_contrast',
            'uppercase_ratio', 'punctuation_density', 'prediction_stability'
        ]
        
        # 低重要性特征
        low_importance = [
            'word_count', 'lexical_diversity', 'sentence_length',
            'has_repeated_chars', 'has_uppercase_words'
        ]
        
        # 分配权重
        for feature_name, value in features.items():
            if feature_name in high_importance:
                importance_scores[feature_name] = 1.0
            elif feature_name in medium_importance:
                importance_scores[feature_name] = 0.6
            elif feature_name in low_importance:
                importance_scores[feature_name] = 0.3
            else:
                importance_scores[feature_name] = 0.5  # 默认权重
        
        return importance_scores
