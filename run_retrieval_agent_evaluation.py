#!/usr/bin/env python3
"""
向量检索智能体评估脚本
Retrieval-Augmented Agent Evaluation Script

用于消融实验，只使用向量检索智能体进行仇恨言论检测评估
格式和模式与现有评估脚本保持一致

作者: AI Assistant
日期: 2025-08-04
版本: 1.0
"""

import argparse
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from offensive_speech_detection.models import RetrievalAugmentedDetector, VectorDatabaseManager
from offensive_speech_detection.evaluator import ModelEvaluator
from offensive_speech_detection.data_loader import get_dataset_loader

class RetrievalAgentDetector:
    """向量检索智能体检测器包装类，用于与评估器兼容"""
    
    def __init__(self, model="gpt-3.5-turbo-0125", provider="api", ollama_base_url="http://localhost:11434",
                 local_model_path=None, dataset_name=None):
        """
        初始化向量检索智能体检测器

        Args:
            model: 模型名称
            provider: 模型提供者 (api/ollama/local)
            ollama_base_url: Ollama API基础URL
            local_model_path: 本地模型路径
            dataset_name: 数据集名称（用于向量检索）
        """
        self.dataset_name = dataset_name
        self.model = model  # 保存模型名称用于日志

        # 初始化向量数据库管理器
        self.vector_db_manager = VectorDatabaseManager()

        # 初始化检索增强检测器
        self.retrieval_detector = RetrievalAugmentedDetector(
            model=model,
            provider=provider,
            ollama_base_url=ollama_base_url,
            local_model_path=local_model_path,
            vector_db_manager=self.vector_db_manager
        )

        # 确保向量数据库存在
        self._ensure_vector_database()
    
    def _ensure_vector_database(self):
        """确保向量数据库存在，如果不存在则创建"""
        if not self.vector_db_manager.dataset_has_vectordb(self.dataset_name):
            print(f"Vector database does not exist, creating for dataset {self.dataset_name}...")
            
            # 加载数据集
            dataset_loader = get_dataset_loader(self.dataset_name)
            dataset = dataset_loader.load_dataset()
            
            # 提取文本和标签
            texts = []
            labels = []
            metadata = []
            
            for item in dataset:
                if isinstance(item, dict):
                    text = item.get('text', str(item))
                    label = item.get('label', 0)
                else:
                    text = str(item)
                    label = 0
                
                texts.append(text)
                labels.append(label)
                metadata.append({
                    'label': label,
                    'dataset': self.dataset_name
                })
            
            # 创建向量数据库
            success = self.retrieval_detector.build_vector_database(
                dataset_name=self.dataset_name,
                texts=texts,
                labels=labels,
                metadata=metadata
            )
            
            if success:
                print(f"Vector database created successfully")
            else:
                print(f"Vector database creation failed")
                raise RuntimeError(f"Failed to create vector database for {self.dataset_name}")
        else:
            print(f"Vector database already exists for dataset: {self.dataset_name}")
    
    def detect(self, text):
        """
        检测文本是否为仇恨言论
        
        Args:
            text: 输入文本
            
        Returns:
            dict: 检测结果，包含verdict, explanation, processing_time等
        """
        # 使用检索增强检测器进行检测
        result = self.retrieval_detector.detect(text, self.dataset_name)
        
        # 确保结果格式与其他检测器一致
        if 'verdict' not in result:
            result['verdict'] = 0  # 默认为非仇恨言论
        
        if 'explanation' not in result:
            result['explanation'] = "No explanation provided"
        
        if 'processing_time' not in result:
            result['processing_time'] = 0.0
        
        # 添加检索特定的信息
        result['detector_type'] = 'retrieval_agent'
        result['dataset_used'] = self.dataset_name
        
        return result

def main():
    """运行向量检索智能体评估"""
    parser = argparse.ArgumentParser(description="Run retrieval-augmented agent evaluation for ablation study")
    parser.add_argument("--dataset", type=str, default="ImplicitHate",
                        choices=["ImplicitHate", "HateSpeechOffensive", "HateSpeechStormfront"],
                        help="Name of the dataset to evaluate")
    parser.add_argument("--num_samples", type=int, default=100,
                        help="Number of samples to evaluate")
    parser.add_argument("--start_idx", type=int, default=0,
                        help="Starting index of samples")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo-0125",
                        help="Name of the model to use (only required for api/ollama providers)")
    parser.add_argument("--provider", type=str, choices=["api", "ollama", "local"], default="api",
                        help="Model provider")
    parser.add_argument("--ollama-base-url", type=str, default="http://localhost:11434",
                        help="Ollama API base URL")
    parser.add_argument("--local-model-path", type=str,
                        help="Path to local model (used when provider is 'local')")

    args = parser.parse_args()

    if args.provider == "local" and not args.local_model_path:
        parser.error("--local-model-path is required when using a local provider.")

    # 当使用本地模型时，自动从路径解析模型名称
    if args.provider == "local":
        args.model = os.path.basename(args.local_model_path.rstrip('/\\'))

    print(f"Starting retrieval-augmented agent evaluation on {args.dataset} dataset")
    print(f"Number of samples: {args.num_samples}, starting index: {args.start_idx}")
    print(f"Model provider: {args.provider}, model: {args.model}")

    model_path = args.model
    if args.provider == "local":
        model_path = args.local_model_path
        print(f"Using local model at: {model_path}")

    # 创建向量检索智能体检测器
    detector = RetrievalAgentDetector(
        model=args.model,  # 使用提取的模型名称，不是完整路径
        provider=args.provider,
        ollama_base_url=args.ollama_base_url,
        local_model_path=model_path,  # 完整路径用于实际加载模型
        dataset_name=args.dataset  # 数据集名称用于向量检索
    )
    
    # 创建评估器
    evaluator = ModelEvaluator(
        detector=detector,
        dataset_name=args.dataset,
        num_samples=args.num_samples,
        start_idx=args.start_idx
    )
    
    # 运行评估
    print(f"\nStarting retrieval-augmented agent evaluation...")
    evaluator.evaluate()

    # 绘制指标图
    evaluator.plot_metrics()

    print(f"\nRetrieval-augmented agent evaluation completed")
    print(f"Log files saved to logs/ directory")
    print(f"Result charts saved to results/ directory")

if __name__ == "__main__":
    main()
