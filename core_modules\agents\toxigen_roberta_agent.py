#!/usr/bin/env python3
"""
ToxiGenRoBERTa智能体实现
专门用于仇恨言论检测的ToxiGenRoBERTa模型智能体
基于TOXIGEN项目训练，专门针对隐式和显式仇恨言论检测
"""

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import numpy as np
from typing import Dict, List, Optional
import time
import logging
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ToxiGenRoBERTaConfig:
    """ToxiGenRoBERTa智能体配置"""
    model_path: str = "D:/models/toxigen_roberta"
    max_length: int = 512
    device: str = "auto"  # auto, cuda, cpu
    cache_size: int = 1000

class ToxiGenRoBERTaAgent:
    """
    ToxiGenRoBERTa仇恨言论检测智能体
    基于TOXIGEN项目训练，专门针对隐式和显式仇恨言论检测
    使用对抗性生成训练，在复杂仇恨言论识别任务上表现优异
    """

    def __init__(self, config: ToxiGenRoBERTaConfig = None):
        """
        初始化ToxiGenRoBERTa智能体

        Args:
            config: 配置对象
        """
        self.config = config or ToxiGenRoBERTaConfig()

        # 设备选择
        if self.config.device == "auto":
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(self.config.device)

        logger.info(f"ToxiGenRoBERTa智能体使用设备: {self.device}")

        # 预测缓存
        self.prediction_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0

        # 性能统计
        self.total_predictions = 0
        self.total_processing_time = 0.0

        # 加载模型
        self._load_model()

    def _load_model(self):
        """加载ToxiGenRoBERTa模型和分词器"""
        try:
            logger.info(f"正在加载ToxiGenRoBERTa模型: {self.config.model_path}")

            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(self.config.model_path)
            logger.info("✅ ToxiGenRoBERTa分词器加载成功")

            # 加载模型
            self.model = AutoModelForSequenceClassification.from_pretrained(self.config.model_path)
            self.model.to(self.device)
            self.model.eval()
            logger.info("✅ ToxiGenRoBERTa模型加载成功")

            # 获取标签映射
            self.id2label = self.model.config.id2label
            self.label2id = self.model.config.label2id
            logger.info(f"标签映射: {self.id2label}")

        except Exception as e:
            logger.error(f"❌ ToxiGenRoBERTa模型加载失败: {e}")
            raise
    
    def detect(self, text: str, method: str = "classification") -> Dict:
        """
        检测文本是否包含仇恨言论

        Args:
            text: 输入文本
            method: 检测方法（保持与DeBERTa接口一致）

        Returns:
            检测结果字典
        """
        # 检查缓存
        cache_key = f"toxigen_roberta_{hash(text)}"
        if cache_key in self.prediction_cache:
            self.cache_hits += 1
            cached_result = self.prediction_cache[cache_key].copy()
            cached_result["from_cache"] = True
            return cached_result

        self.cache_misses += 1
        start_time = time.time()

        try:
            # 分词
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=self.config.max_length
            )
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # 推理
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits

            # 计算概率
            probs = F.softmax(logits, dim=-1)
            predicted_class_id = logits.argmax().item()

            # 获取预测标签
            predicted_label = self.id2label[predicted_class_id]

            # 判断是否为仇恨言论 (ToxiGen使用LABEL_0/LABEL_1格式)
            if predicted_label == "LABEL_1":
                verdict = 1  # 仇恨言论/毒性内容
            elif predicted_label == "LABEL_0":
                verdict = 0  # 正常文本
            else:
                # 备用判断：基于概率分数
                verdict = 1 if probs[0][1].item() > 0.5 else 0

            processing_time = time.time() - start_time

            # 计算置信度（使用最大概率）
            max_prob = max(probs[0]).item()
            confidence = max_prob

            # 构建结果
            result = {
                "verdict": verdict,
                "confidence": confidence,  # 添加置信度字段
                "predicted_label": predicted_label,
                "all_probabilities": {
                    self.id2label[i]: prob.item()
                    for i, prob in enumerate(probs[0])
                },
                "processing_time": processing_time,
                "method": "toxigen_roberta_classification",
                "from_cache": False,
                "reasoning": f"ToxiGenRoBERTa预测: {predicted_label} (毒性概率: {probs[0][1].item():.3f}, 置信度: {confidence:.3f})"
            }

            # 缓存结果
            if len(self.prediction_cache) < self.config.cache_size:
                self.prediction_cache[cache_key] = result.copy()

            # 更新统计
            self.total_predictions += 1
            self.total_processing_time += processing_time

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"ToxiGenRoBERTa检测错误: {e}")

            return {
                "verdict": 0,
                "confidence": 0.5,  # 错误时使用默认置信度
                "error": str(e),
                "processing_time": processing_time,
                "method": "toxigen_roberta_classification",
                "from_cache": False,
                "reasoning": f"检测失败: {str(e)}"
            }
    
    def batch_detect(self, texts: List[str]) -> List[Dict]:
        """批量检测"""
        results = []
        for text in texts:
            result = self.detect(text)
            results.append(result)
        return results
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0
        
        return {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": hit_rate,
            "cache_size": len(self.prediction_cache),
            "max_cache_size": self.config.cache_size
        }
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        avg_time = (self.total_processing_time / self.total_predictions 
                   if self.total_predictions > 0 else 0)
        
        return {
            "total_predictions": self.total_predictions,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_time,
            "predictions_per_second": (self.total_predictions / self.total_processing_time 
                                     if self.total_processing_time > 0 else 0)
        }
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            "model_path": self.config.model_path,
            "device": str(self.device),
            "num_labels": len(self.id2label),
            "labels": list(self.id2label.values()),
            "model_type": self.model.config.model_type,
            "vocab_size": self.model.config.vocab_size,
            "max_length": self.config.max_length
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.prediction_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("ToxiGenRoBERTa智能体缓存已清空")

# 测试函数
def test_toxigen_roberta_agent():
    """测试ToxiGenRoBERTa智能体"""
    print("🧪 ToxiGenRoBERTa智能体测试")
    print("=" * 60)

    try:
        # 创建智能体
        agent = ToxiGenRoBERTaAgent()

        # 显示模型信息
        model_info = agent.get_model_info()
        print(f"\n📋 模型信息:")
        for key, value in model_info.items():
            print(f"   {key}: {value}")

        # 测试文本
        test_texts = [
            "I love this beautiful day!",
            "You are so stupid and worthless!",
            "I hate all people from that country",
            "Thank you for your help",
            "Those people are just naturally inferior",  # 隐式仇恨言论测试
            "Some groups just can't integrate into society"  # 隐式仇恨言论测试
        ]

        print(f"\n🔍 测试结果:")
        print("-" * 60)

        for i, text in enumerate(test_texts, 1):
            result = agent.detect(text)
            verdict_text = "仇恨言论" if result["verdict"] == 1 else "正常文本"

            print(f"\n{i}. 文本: {text}")
            print(f"   预测: {verdict_text}")
            print(f"   处理时间: {result['processing_time']:.3f}s")
            print(f"   推理: {result['reasoning']}")

        # 缓存测试
        print(f"\n🔄 缓存测试:")
        result1 = agent.detect(test_texts[0])
        result2 = agent.detect(test_texts[0])  # 应该从缓存获取
        print(f"   第一次调用: {result1['processing_time']:.3f}s")
        print(f"   第二次调用: {result2['processing_time']:.3f}s (缓存: {result2['from_cache']})")

        # 统计信息
        cache_stats = agent.get_cache_stats()
        perf_stats = agent.get_performance_stats()

        print(f"\n📊 统计信息:")
        print(f"   缓存命中率: {cache_stats['hit_rate']:.1%}")
        print(f"   平均处理时间: {perf_stats['average_processing_time']:.3f}s")
        print(f"   每秒处理数: {perf_stats['predictions_per_second']:.1f}")

        print(f"\n✅ ToxiGenRoBERTa智能体测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_toxigen_roberta_agent()
