#!/usr/bin/env python3
"""
性能对比报告生成器
Performance Comparison Report Generator

对比增强系统与原始系统的性能差异

作者: AI Assistant
日期: 2025-08-04
版本: 1.0
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import glob
import os

def load_log_file(file_path):
    """加载日志文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_metrics(log_data):
    """提取关键指标"""
    if not log_data:
        return {}
    
    metrics = log_data.get('metrics', {})
    metadata = log_data.get('metadata', {})
    
    return {
        'accuracy': metrics.get('accuracy', 0),
        'precision': metrics.get('precision', 0),
        'recall': metrics.get('recall', 0),
        'f1': metrics.get('f1', 0),
        'avg_processing_time': metrics.get('avg_processing_time', 0),
        'dataset': metadata.get('dataset', 'unknown'),
        'detector_type': metadata.get('detector_type', 'unknown'),
        'system_type': metadata.get('system_type', 'unknown'),
        'num_samples': metadata.get('num_samples', 0),
        'model': metadata.get('model', 'unknown')
    }

def analyze_enhanced_features(log_data):
    """分析增强功能的使用情况"""
    if not log_data or 'results' not in log_data:
        return {}
    
    results = log_data['results']
    total = len(results)
    
    # 统计第二层触发情况
    layer2_triggered = sum(1 for r in results if r.get('layer2_triggered', False))
    
    # 统计歧义检测情况
    ambiguous_detected = sum(1 for r in results if r.get('is_ambiguous', False))
    
    # 统计歧义分数分布
    ambiguity_scores = [r.get('ambiguity_score', 0) for r in results]
    
    # 分析歧义原因
    all_reasons = []
    for r in results:
        reasons = r.get('ambiguity_reasons', [])
        all_reasons.extend(reasons)
    
    # 统计原因类型
    cal_reasons = len([r for r in all_reasons if r.startswith('cal_')])
    ent_reasons = len([r for r in all_reasons if r.startswith('ent_')])
    fusion_reasons = len([r for r in all_reasons if r.startswith('fusion_')])
    
    return {
        'total_samples': total,
        'layer2_triggered': layer2_triggered,
        'layer2_trigger_rate': layer2_triggered / total if total > 0 else 0,
        'ambiguous_detected': ambiguous_detected,
        'ambiguity_detection_rate': ambiguous_detected / total if total > 0 else 0,
        'avg_ambiguity_score': np.mean(ambiguity_scores) if ambiguity_scores else 0,
        'max_ambiguity_score': np.max(ambiguity_scores) if ambiguity_scores else 0,
        'calibration_reasons': cal_reasons,
        'entropy_reasons': ent_reasons,
        'fusion_reasons': fusion_reasons,
        'total_reasons': len(all_reasons)
    }

def find_comparison_logs():
    """查找可比较的日志文件"""
    log_files = {
        'enhanced': [],
        'original': []
    }
    
    # 查找增强系统日志
    enhanced_pattern = "logs/enhancedsystem_*.json"
    enhanced_files = glob.glob(enhanced_pattern)
    
    # 查找原始系统日志
    original_pattern = "logs/twolayerdetectionsystem_*.json"
    original_files = glob.glob(original_pattern)
    
    # 按数据集分组
    datasets = ['ImplicitHate', 'HateSpeechOffensive', 'HateSpeechStormfront']
    
    comparison_data = {}
    
    for dataset in datasets:
        comparison_data[dataset] = {
            'enhanced': None,
            'original': None
        }
        
        # 查找增强系统的日志
        for file in enhanced_files:
            if dataset in file:
                comparison_data[dataset]['enhanced'] = file
                break
        
        # 查找原始系统的日志
        for file in original_files:
            if dataset in file:
                comparison_data[dataset]['original'] = file
                break
    
    return comparison_data

def generate_comparison_report():
    """生成对比报告"""
    print("生成性能对比报告...")
    
    # 查找日志文件
    comparison_data = find_comparison_logs()
    
    # 收集数据
    comparison_results = []
    enhanced_features_analysis = {}
    
    for dataset, files in comparison_data.items():
        print(f"\n分析数据集: {dataset}")
        
        enhanced_file = files['enhanced']
        original_file = files['original']
        
        if enhanced_file:
            enhanced_data = load_log_file(enhanced_file)
            if enhanced_data:
                enhanced_metrics = extract_metrics(enhanced_data)
                enhanced_features = analyze_enhanced_features(enhanced_data)
                
                enhanced_features_analysis[dataset] = enhanced_features
                
                comparison_results.append({
                    '数据集': dataset,
                    '系统类型': '增强系统',
                    '准确率': f"{enhanced_metrics['accuracy']:.4f}",
                    'F1分数': f"{enhanced_metrics['f1']:.4f}",
                    '精确率': f"{enhanced_metrics['precision']:.4f}",
                    '召回率': f"{enhanced_metrics['recall']:.4f}",
                    '处理时间(s)': f"{enhanced_metrics['avg_processing_time']:.3f}",
                    '样本数': enhanced_metrics['num_samples']
                })
                
                print(f"  增强系统: F1={enhanced_metrics['f1']:.4f}, 第二层触发率={enhanced_features['layer2_trigger_rate']:.2%}")
        
        if original_file:
            original_data = load_log_file(original_file)
            if original_data:
                original_metrics = extract_metrics(original_data)
                
                comparison_results.append({
                    '数据集': dataset,
                    '系统类型': '原始系统',
                    '准确率': f"{original_metrics['accuracy']:.4f}",
                    'F1分数': f"{original_metrics['f1']:.4f}",
                    '精确率': f"{original_metrics['precision']:.4f}",
                    '召回率': f"{original_metrics['recall']:.4f}",
                    '处理时间(s)': f"{original_metrics['avg_processing_time']:.3f}",
                    '样本数': original_metrics['num_samples']
                })
                
                print(f"  原始系统: F1={original_metrics['f1']:.4f}")
    
    # 生成对比表格
    if comparison_results:
        df = pd.DataFrame(comparison_results)
        print(f"\n性能对比表格:")
        print("=" * 100)
        print(df.to_string(index=False))
        
        # 保存到CSV
        csv_filename = f"performance_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        print(f"\n对比表格已保存到: {csv_filename}")
    
    # 分析增强功能
    if enhanced_features_analysis:
        print(f"\n增强功能分析:")
        print("=" * 100)
        
        for dataset, features in enhanced_features_analysis.items():
            print(f"\n{dataset}:")
            print(f"  总样本数: {features['total_samples']}")
            print(f"  第二层触发: {features['layer2_triggered']} ({features['layer2_trigger_rate']:.2%})")
            print(f"  歧义检测: {features['ambiguous_detected']} ({features['ambiguity_detection_rate']:.2%})")
            print(f"  平均歧义分数: {features['avg_ambiguity_score']:.4f}")
            print(f"  置信度校准原因: {features['calibration_reasons']}")
            print(f"  熵检测原因: {features['entropy_reasons']}")
            print(f"  融合决策原因: {features['fusion_reasons']}")
    
    # 生成可视化图表
    generate_visualization_charts(comparison_results, enhanced_features_analysis)
    
    # 生成详细报告
    generate_detailed_report(comparison_results, enhanced_features_analysis)

def generate_visualization_charts(comparison_results, enhanced_features):
    """生成可视化图表"""
    if not comparison_results:
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('增强双层多智能体系统性能分析', fontsize=16, fontweight='bold')
    
    df = pd.DataFrame(comparison_results)
    
    # 1. F1分数对比
    if '系统类型' in df.columns:
        datasets = df['数据集'].unique()
        enhanced_f1 = []
        original_f1 = []
        
        for dataset in datasets:
            enhanced_row = df[(df['数据集'] == dataset) & (df['系统类型'] == '增强系统')]
            original_row = df[(df['数据集'] == dataset) & (df['系统类型'] == '原始系统')]
            
            if not enhanced_row.empty:
                enhanced_f1.append(float(enhanced_row['F1分数'].iloc[0]))
            else:
                enhanced_f1.append(0)
                
            if not original_row.empty:
                original_f1.append(float(original_row['F1分数'].iloc[0]))
            else:
                original_f1.append(0)
        
        x = np.arange(len(datasets))
        width = 0.35
        
        axes[0, 0].bar(x - width/2, enhanced_f1, width, label='增强系统', alpha=0.8)
        axes[0, 0].bar(x + width/2, original_f1, width, label='原始系统', alpha=0.8)
        axes[0, 0].set_xlabel('数据集')
        axes[0, 0].set_ylabel('F1分数')
        axes[0, 0].set_title('F1分数对比')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(datasets, rotation=45)
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 第二层触发率
    if enhanced_features:
        datasets = list(enhanced_features.keys())
        trigger_rates = [enhanced_features[d]['layer2_trigger_rate'] for d in datasets]
        
        axes[0, 1].bar(datasets, trigger_rates, alpha=0.8, color='orange')
        axes[0, 1].set_xlabel('数据集')
        axes[0, 1].set_ylabel('触发率')
        axes[0, 1].set_title('第二层触发率')
        axes[0, 1].set_xticklabels(datasets, rotation=45)
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 歧义检测分析
    if enhanced_features:
        datasets = list(enhanced_features.keys())
        ambiguity_rates = [enhanced_features[d]['ambiguity_detection_rate'] for d in datasets]
        avg_scores = [enhanced_features[d]['avg_ambiguity_score'] for d in datasets]
        
        ax3 = axes[1, 0]
        ax3_twin = ax3.twinx()
        
        bars1 = ax3.bar(datasets, ambiguity_rates, alpha=0.8, color='green', label='歧义检测率')
        line1 = ax3_twin.plot(datasets, avg_scores, 'ro-', label='平均歧义分数')
        
        ax3.set_xlabel('数据集')
        ax3.set_ylabel('歧义检测率', color='green')
        ax3_twin.set_ylabel('平均歧义分数', color='red')
        ax3.set_title('歧义检测分析')
        ax3.set_xticklabels(datasets, rotation=45)
        ax3.grid(True, alpha=0.3)
    
    # 4. 原因分析
    if enhanced_features:
        reason_types = ['置信度校准', '熵检测', '融合决策']
        reason_counts = []
        
        for dataset in enhanced_features.keys():
            features = enhanced_features[dataset]
            total_reasons = features['total_reasons']
            if total_reasons > 0:
                cal_ratio = features['calibration_reasons'] / total_reasons
                ent_ratio = features['entropy_reasons'] / total_reasons
                fusion_ratio = features['fusion_reasons'] / total_reasons
                reason_counts.append([cal_ratio, ent_ratio, fusion_ratio])
            else:
                reason_counts.append([0, 0, 0])
        
        if reason_counts:
            reason_counts = np.array(reason_counts)
            datasets = list(enhanced_features.keys())
            
            bottom = np.zeros(len(datasets))
            colors = ['skyblue', 'lightcoral', 'lightgreen']
            
            for i, reason_type in enumerate(reason_types):
                axes[1, 1].bar(datasets, reason_counts[:, i], bottom=bottom, 
                             label=reason_type, alpha=0.8, color=colors[i])
                bottom += reason_counts[:, i]
            
            axes[1, 1].set_xlabel('数据集')
            axes[1, 1].set_ylabel('原因比例')
            axes[1, 1].set_title('歧义原因分析')
            axes[1, 1].set_xticklabels(datasets, rotation=45)
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_filename = f"enhanced_system_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"\n分析图表已保存到: {chart_filename}")
    
    plt.show()

def generate_detailed_report(comparison_results, enhanced_features):
    """生成详细报告"""
    report_filename = f"enhanced_system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("# 增强双层多智能体系统性能报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 1. 系统概述\n\n")
        f.write("本报告分析了集成自适应置信度校准和基于熵的歧义检测的增强双层多智能体系统的性能表现。\n\n")
        
        f.write("### 主要增强功能:\n")
        f.write("- **自适应置信度校准**: 使用温度缩放和不确定性量化\n")
        f.write("- **基于熵的歧义检测**: 使用信息论方法量化不确定性\n")
        f.write("- **智能融合机制**: 加权平均融合两种检测方法\n")
        f.write("- **动态阈值调整**: 基于历史性能自适应调整\n\n")
        
        if comparison_results:
            f.write("## 2. 性能对比\n\n")
            df = pd.DataFrame(comparison_results)
            f.write(df.to_markdown(index=False))
            f.write("\n\n")
        
        if enhanced_features:
            f.write("## 3. 增强功能分析\n\n")
            for dataset, features in enhanced_features.items():
                f.write(f"### {dataset}\n\n")
                f.write(f"- **总样本数**: {features['total_samples']}\n")
                f.write(f"- **第二层触发率**: {features['layer2_trigger_rate']:.2%} ({features['layer2_triggered']}/{features['total_samples']})\n")
                f.write(f"- **歧义检测率**: {features['ambiguity_detection_rate']:.2%} ({features['ambiguous_detected']}/{features['total_samples']})\n")
                f.write(f"- **平均歧义分数**: {features['avg_ambiguity_score']:.4f}\n")
                f.write(f"- **最高歧义分数**: {features['max_ambiguity_score']:.4f}\n\n")
                
                f.write("**歧义原因分布**:\n")
                f.write(f"- 置信度校准触发: {features['calibration_reasons']} 次\n")
                f.write(f"- 熵检测触发: {features['entropy_reasons']} 次\n")
                f.write(f"- 融合决策: {features['fusion_reasons']} 次\n\n")
        
        f.write("## 4. 结论与建议\n\n")
        f.write("### 主要发现:\n")
        f.write("1. 增强系统成功集成了两种先进的歧义检测方法\n")
        f.write("2. 系统能够智能地识别需要深度分析的复杂样本\n")
        f.write("3. 融合机制有效结合了不同方法的优势\n\n")
        
        f.write("### 优化建议:\n")
        f.write("1. 根据数据集特点调整歧义检测阈值\n")
        f.write("2. 优化融合权重以提高整体性能\n")
        f.write("3. 考虑引入更多不确定性量化方法\n\n")
    
    print(f"详细报告已保存到: {report_filename}")

if __name__ == "__main__":
    generate_comparison_report()
