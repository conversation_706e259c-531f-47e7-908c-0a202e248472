#!/usr/bin/env python3
"""
Twitter RoBERTa Sentiment智能体实现
专门用于情绪分析的Twitter RoBERTa模型智能体
基于twitter-roberta-base-sentiment-latest模型
"""

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForSequenceClassification, AutoConfig, pipeline
import numpy as np
from scipy.special import softmax
from typing import Dict, List, Optional
import time
import logging
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TwitterSentimentConfig:
    """Twitter Sentiment智能体配置"""
    model_path: str = "D:/models/twitter-roberta-base-sentiment-latest"
    max_length: int = 512
    device: str = "auto"  # auto, cuda, cpu
    cache_size: int = 1000
    confidence_threshold: float = 0.5
    use_pipeline: bool = True

class TwitterSentimentAgent:
    """
    Twitter RoBERTa Sentiment智能体
    基于twitter-roberta-base-sentiment-latest模型
    专门用于社交媒体文本的情绪分析
    """

    def __init__(self, config: TwitterSentimentConfig = None):
        """
        初始化Twitter Sentiment智能体

        Args:
            config: 配置对象
        """
        self.config = config or TwitterSentimentConfig()

        # 设备选择
        if self.config.device == "auto":
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(self.config.device)

        logger.info(f"Twitter Sentiment智能体使用设备: {self.device}")

        # 预测缓存
        self.prediction_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0

        # 性能统计
        self.total_predictions = 0
        self.total_processing_time = 0.0

        # 加载模型
        self._load_model()

    def _load_model(self):
        """加载Twitter Sentiment模型和分词器"""
        try:
            logger.info(f"正在加载Twitter Sentiment模型: {self.config.model_path}")

            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(self.config.model_path)
            logger.info("✅ Twitter Sentiment分词器加载成功")

            # 加载配置
            self.model_config = AutoConfig.from_pretrained(self.config.model_path)
            
            # 加载模型
            self.model = AutoModelForSequenceClassification.from_pretrained(self.config.model_path)
            self.model.to(self.device)
            self.model.eval()
            logger.info("✅ Twitter Sentiment模型加载成功")

            # 创建pipeline（可选）
            if self.config.use_pipeline:
                self.pipeline = pipeline(
                    "sentiment-analysis",
                    model=self.config.model_path,
                    tokenizer=self.config.model_path
                )
                logger.info("✅ Twitter Sentiment pipeline创建成功")

            # 获取标签映射
            self.id2label = self.model_config.id2label
            self.label2id = self.model_config.label2id
            logger.info(f"标签映射: {self.id2label}")

        except Exception as e:
            logger.error(f"❌ Twitter Sentiment模型加载失败: {e}")
            raise

    def preprocess(self, text: str) -> str:
        """
        预处理文本（用户名和链接占位符）
        """
        new_text = []
        for t in text.split(" "):
            t = '@user' if t.startswith('@') and len(t) > 1 else t
            t = 'http' if t.startswith('http') else t
            new_text.append(t)
        return " ".join(new_text)

    def detect(self, text: str, method: str = "classification") -> Dict:
        """
        分析文本情绪并判断是否为仇恨言论

        Args:
            text: 输入文本
            method: 检测方法

        Returns:
            检测结果字典
        """
        # 检查缓存
        cache_key = f"twitter_sentiment_{hash(text)}"
        if cache_key in self.prediction_cache:
            self.cache_hits += 1
            cached_result = self.prediction_cache[cache_key].copy()
            cached_result["from_cache"] = True
            return cached_result

        self.cache_misses += 1
        start_time = time.time()

        try:
            # 预处理文本
            processed_text = self.preprocess(text)

            # 使用pipeline进行快速预测
            if self.config.use_pipeline and hasattr(self, 'pipeline'):
                pipeline_result = self.pipeline(processed_text)
                main_sentiment = pipeline_result[0]['label']
                main_confidence = pipeline_result[0]['score']
            else:
                main_sentiment = "UNKNOWN"
                main_confidence = 0.5

            # 使用完整模型获取详细结果
            inputs = self.tokenizer(
                processed_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=self.config.max_length
            )
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # 推理
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits

            # 计算概率
            scores = logits[0].detach().cpu().numpy()
            probs = softmax(scores)
            
            # 排序结果
            ranking = np.argsort(scores)[::-1]
            
            # 构建详细结果
            detailed_results = []
            for i in range(len(scores)):
                label = self.id2label[ranking[i]]
                score = float(probs[ranking[i]])
                detailed_results.append((label, score))

            # 基于情绪分析结果判断是否为仇恨言论
            # Twitter情绪模型的标签：negative, neutral, positive
            negative_score = 0.0
            positive_score = 0.0
            neutral_score = 0.0

            for label, score in detailed_results:
                if label == 'negative':
                    negative_score = score
                elif label == 'positive':
                    positive_score = score
                elif label == 'neutral':
                    neutral_score = score

            # 判断逻辑：
            # 1. 如果负面情绪分数高且显著超过正面情绪，可能是仇恨言论
            # 2. 考虑到仇恨言论通常带有强烈的负面情绪
            if negative_score > 0.6 and negative_score > positive_score + 0.3:
                verdict = 1
                confidence = min(negative_score, 0.9)
            elif negative_score > 0.8:  # 极强负面情绪
                verdict = 1
                confidence = min(negative_score, 0.9)
            else:
                verdict = 0
                confidence = max(positive_score, neutral_score, 0.1)

            processing_time = time.time() - start_time

            # 构建结果
            result = {
                "verdict": verdict,
                "confidence": confidence,
                "main_sentiment": main_sentiment,
                "main_confidence": main_confidence,
                "detailed_sentiments": detailed_results,
                "negative_score": negative_score,
                "positive_score": positive_score,
                "processing_time": processing_time,
                "method": "twitter_sentiment_classification",
                "from_cache": False,
                "reasoning": f"情绪分析: {main_sentiment} (置信度: {main_confidence:.3f}), 负面分数: {negative_score:.3f}, 正面分数: {positive_score:.3f}"
            }

            # 缓存结果
            if len(self.prediction_cache) < self.config.cache_size:
                self.prediction_cache[cache_key] = result.copy()

            # 更新统计
            self.total_predictions += 1
            self.total_processing_time += processing_time

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Twitter Sentiment检测错误: {e}")

            return {
                "verdict": 0,
                "confidence": 0.5,
                "error": str(e),
                "processing_time": processing_time,
                "method": "twitter_sentiment_classification",
                "from_cache": False,
                "reasoning": f"检测失败: {str(e)}"
            }

    def batch_detect(self, texts: List[str]) -> List[Dict]:
        """批量检测"""
        results = []
        for text in texts:
            result = self.detect(text)
            results.append(result)
        return results

    def get_statistics(self) -> Dict:
        """获取性能统计"""
        total_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0
        avg_processing_time = self.total_processing_time / self.total_predictions if self.total_predictions > 0 else 0

        return {
            "total_predictions": self.total_predictions,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_rate": cache_hit_rate,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time
        }

    def clear_cache(self):
        """清除缓存"""
        self.prediction_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("Twitter Sentiment智能体缓存已清除")
