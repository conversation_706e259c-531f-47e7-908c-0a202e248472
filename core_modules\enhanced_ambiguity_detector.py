#!/usr/bin/env python3
"""
增强歧义检测器
Enhanced Ambiguity Detector

集成自适应置信度校准和基于熵的歧义检测功能
用于替换现有的歧义检测机制

作者: AI Assistant
日期: 2025-08-04
版本: 1.0
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass

from .confidence_calibration import (
    AdaptiveConfidenceCalibrator,
    CalibrationConfig
)
from .entropy_ambiguity_detection import (
    EntropyBasedAmbiguityDetector,
    EntropyDetectionConfig
)

logger = logging.getLogger(__name__)

@dataclass
class EnhancedAmbiguityConfig:
    """增强歧义检测配置"""
    # 方法选择
    use_confidence_calibration: bool = True
    use_entropy_detection: bool = True
    fusion_method: str = "weighted_average"  # "weighted_average", "max", "consensus"
    
    # 融合权重
    calibration_weight: float = 0.6
    entropy_weight: float = 0.4
    
    # 决策阈值
    final_ambiguity_threshold: float = 0.3  # 降低阈值，更敏感
    consensus_threshold: float = 0.8  # 两种方法一致性阈值
    
    # 子配置
    calibration_config: CalibrationConfig = None
    entropy_config: EntropyDetectionConfig = None
    
    def __post_init__(self):
        if self.calibration_config is None:
            self.calibration_config = CalibrationConfig()
        if self.entropy_config is None:
            self.entropy_config = EntropyDetectionConfig()

class EnhancedAmbiguityDetector:
    """增强歧义检测器主类"""
    
    def __init__(self, config: EnhancedAmbiguityConfig = None):
        self.config = config or EnhancedAmbiguityConfig()
        
        # 初始化子检测器
        self.confidence_calibrator = None
        self.entropy_detector = None
        
        if self.config.use_confidence_calibration:
            self.confidence_calibrator = AdaptiveConfidenceCalibrator(
                self.config.calibration_config
            )
            
        if self.config.use_entropy_detection:
            self.entropy_detector = EntropyBasedAmbiguityDetector(
                self.config.entropy_config
            )
        
        # 统计信息
        self.detection_stats = {
            'total_detections': 0,
            'calibration_triggered': 0,
            'entropy_triggered': 0,
            'both_triggered': 0,
            'neither_triggered': 0,
            'consensus_rate': 0.0
        }
        
        logger.info("增强歧义检测器初始化完成")
    
    def detect_ambiguity(self, 
                        text: str,
                        model_results: Dict[str, Dict[str, Any]]) -> Tuple[bool, List[str], float]:
        """
        增强歧义检测
        
        Args:
            text: 输入文本
            model_results: 模型结果字典
            
        Returns:
            (是否存在歧义, 歧义原因列表, 歧义分数)
        """
        self.detection_stats['total_detections'] += 1
        
        # 提取模型预测和logits
        toxigen_result = model_results.get('toxigen', {})
        hurtlex_result = model_results.get('hurtlex', {})
        
        # 构造预测概率
        toxigen_pred, hurtlex_pred = self._extract_predictions_and_logits(
            toxigen_result, hurtlex_result
        )
        
        # 方法1: 自适应置信度校准
        calibration_result = None
        if self.confidence_calibrator:
            calibration_result = self._run_confidence_calibration(
                toxigen_pred, hurtlex_pred, text
            )
        
        # 方法2: 基于熵的歧义检测
        entropy_result = None
        if self.entropy_detector:
            entropy_result = self._run_entropy_detection(text, model_results)
        
        # 融合结果
        final_result = self._fuse_detection_results(
            calibration_result, entropy_result, text
        )
        
        # 更新统计信息
        self._update_detection_stats(calibration_result, entropy_result)
        
        return (
            bool(final_result['is_ambiguous']),
            final_result['ambiguity_reasons'],
            float(final_result['ambiguity_score'])
        )
    
    def _extract_predictions_and_logits(self, 
                                      toxigen_result: Dict[str, Any],
                                      hurtlex_result: Dict[str, Any]) -> Tuple[np.ndarray, np.ndarray]:
        """提取预测概率和logits"""
        # 从置信度和判决构造概率分布
        def result_to_prob(result):
            confidence = result.get('confidence', 0.5)
            verdict = result.get('verdict', 0)
            
            if verdict == 1:
                return np.array([1 - confidence, confidence])
            else:
                return np.array([confidence, 1 - confidence])
        
        toxigen_pred = result_to_prob(toxigen_result)
        hurtlex_pred = result_to_prob(hurtlex_result)
        
        # 确保概率和为1
        toxigen_pred = toxigen_pred / np.sum(toxigen_pred)
        hurtlex_pred = hurtlex_pred / np.sum(hurtlex_pred)
        
        return toxigen_pred, hurtlex_pred
    
    def _run_confidence_calibration(self, 
                                  toxigen_pred: np.ndarray,
                                  hurtlex_pred: np.ndarray,
                                  text: str) -> Dict[str, Any]:
        """运行置信度校准检测"""
        try:
            # 模拟logits（从概率反推）
            toxigen_logits = np.log(toxigen_pred + 1e-10)
            hurtlex_logits = np.log(hurtlex_pred + 1e-10)
            
            # 校准预测
            toxigen_cal, hurtlex_cal = self.confidence_calibrator.calibrate_predictions(
                toxigen_logits, hurtlex_logits
            )
            
            # 分析歧义
            analysis = self.confidence_calibrator.analyze_ambiguity(
                toxigen_cal, hurtlex_cal, text
            )
            
            return {
                'method': 'confidence_calibration',
                'is_ambiguous': analysis['should_trigger_layer2'],
                'ambiguity_score': analysis['ambiguity_score'],
                'trigger_reason': analysis['trigger_reason'],
                'uncertainty_metrics': analysis['uncertainty_metrics'],
                'calibrated_predictions': {
                    'toxigen': toxigen_cal,
                    'hurtlex': hurtlex_cal
                }
            }
        except Exception as e:
            logger.error(f"置信度校准检测失败: {e}")
            return {
                'method': 'confidence_calibration',
                'is_ambiguous': False,
                'ambiguity_score': 0.0,
                'error': str(e)
            }
    
    def _run_entropy_detection(self, 
                             text: str,
                             model_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """运行基于熵的歧义检测"""
        try:
            is_ambiguous, reasons, score = self.entropy_detector.detect_ambiguity(
                text, model_results
            )
            
            return {
                'method': 'entropy_detection',
                'is_ambiguous': is_ambiguous,
                'ambiguity_score': score,
                'ambiguity_reasons': reasons
            }
        except Exception as e:
            logger.error(f"熵检测失败: {e}")
            return {
                'method': 'entropy_detection',
                'is_ambiguous': False,
                'ambiguity_score': 0.0,
                'error': str(e)
            }
    
    def _fuse_detection_results(self, 
                              calibration_result: Optional[Dict[str, Any]],
                              entropy_result: Optional[Dict[str, Any]],
                              text: str) -> Dict[str, Any]:
        """融合检测结果"""
        # 收集有效结果
        valid_results = []
        if calibration_result and 'error' not in calibration_result:
            valid_results.append(calibration_result)
        if entropy_result and 'error' not in entropy_result:
            valid_results.append(entropy_result)
        
        if not valid_results:
            # 没有有效结果，返回默认值
            return {
                'is_ambiguous': False,
                'ambiguity_score': 0.0,
                'ambiguity_reasons': ['no_valid_detection_method'],
                'fusion_method': 'fallback',
                'individual_results': {
                    'calibration': calibration_result,
                    'entropy': entropy_result
                }
            }
        
        if len(valid_results) == 1:
            # 只有一个有效结果
            result = valid_results[0]
            return {
                'is_ambiguous': result['is_ambiguous'],
                'ambiguity_score': result['ambiguity_score'],
                'ambiguity_reasons': self._extract_reasons(result),
                'fusion_method': 'single_method',
                'individual_results': {
                    'calibration': calibration_result,
                    'entropy': entropy_result
                }
            }
        
        # 两个方法都有效，进行融合
        cal_result = calibration_result
        ent_result = entropy_result
        
        if self.config.fusion_method == "weighted_average":
            # 加权平均
            fused_score = (
                self.config.calibration_weight * cal_result['ambiguity_score'] +
                self.config.entropy_weight * ent_result['ambiguity_score']
            )
            
            is_ambiguous = fused_score > self.config.final_ambiguity_threshold
            
        elif self.config.fusion_method == "max":
            # 取最大值
            fused_score = max(cal_result['ambiguity_score'], ent_result['ambiguity_score'])
            is_ambiguous = (cal_result['is_ambiguous'] or ent_result['is_ambiguous'])
            
        elif self.config.fusion_method == "consensus":
            # 一致性决策
            both_agree_ambiguous = (cal_result['is_ambiguous'] and ent_result['is_ambiguous'])
            both_agree_clear = (not cal_result['is_ambiguous'] and not ent_result['is_ambiguous'])
            
            if both_agree_ambiguous:
                is_ambiguous = True
                fused_score = (cal_result['ambiguity_score'] + ent_result['ambiguity_score']) / 2
            elif both_agree_clear:
                is_ambiguous = False
                fused_score = (cal_result['ambiguity_score'] + ent_result['ambiguity_score']) / 2
            else:
                # 分歧情况，使用加权平均
                fused_score = (
                    self.config.calibration_weight * cal_result['ambiguity_score'] +
                    self.config.entropy_weight * ent_result['ambiguity_score']
                )
                is_ambiguous = fused_score > self.config.final_ambiguity_threshold
        else:
            # 默认使用加权平均
            fused_score = (
                self.config.calibration_weight * cal_result['ambiguity_score'] +
                self.config.entropy_weight * ent_result['ambiguity_score']
            )
            is_ambiguous = fused_score > self.config.final_ambiguity_threshold
        
        # 合并原因
        all_reasons = []
        all_reasons.extend(self._extract_reasons(cal_result))
        all_reasons.extend(self._extract_reasons(ent_result))
        
        # 添加融合信息
        fusion_info = f"fusion_{self.config.fusion_method}_score_{fused_score:.3f}"
        all_reasons.append(fusion_info)
        
        return {
            'is_ambiguous': is_ambiguous,
            'ambiguity_score': fused_score,
            'ambiguity_reasons': all_reasons,
            'fusion_method': self.config.fusion_method,
            'individual_results': {
                'calibration': calibration_result,
                'entropy': entropy_result
            }
        }
    
    def _extract_reasons(self, result: Dict[str, Any]) -> List[str]:
        """从结果中提取原因"""
        reasons = []
        
        if result['method'] == 'confidence_calibration':
            if 'trigger_reason' in result:
                reasons.append(f"cal_{result['trigger_reason']}")
        elif result['method'] == 'entropy_detection':
            if 'ambiguity_reasons' in result:
                reasons.extend([f"ent_{reason}" for reason in result['ambiguity_reasons']])
        
        return reasons
    
    def _update_detection_stats(self, 
                              calibration_result: Optional[Dict[str, Any]],
                              entropy_result: Optional[Dict[str, Any]]):
        """更新检测统计信息"""
        cal_triggered = (calibration_result and 
                        calibration_result.get('is_ambiguous', False))
        ent_triggered = (entropy_result and 
                        entropy_result.get('is_ambiguous', False))
        
        if cal_triggered:
            self.detection_stats['calibration_triggered'] += 1
        if ent_triggered:
            self.detection_stats['entropy_triggered'] += 1
        if cal_triggered and ent_triggered:
            self.detection_stats['both_triggered'] += 1
        if not cal_triggered and not ent_triggered:
            self.detection_stats['neither_triggered'] += 1
        
        # 计算一致性率
        if self.detection_stats['total_detections'] > 0:
            consensus_count = (self.detection_stats['both_triggered'] + 
                             self.detection_stats['neither_triggered'])
            self.detection_stats['consensus_rate'] = (
                consensus_count / self.detection_stats['total_detections']
            )
    
    def update_calibration(self, 
                         toxigen_result: Dict[str, Any],
                         hurtlex_result: Dict[str, Any],
                         true_label: int,
                         performance_metrics: Optional[Dict[str, float]] = None):
        """更新校准参数"""
        if self.confidence_calibrator:
            # 提取预测
            toxigen_pred, hurtlex_pred = self._extract_predictions_and_logits(
                toxigen_result, hurtlex_result
            )
            
            # 转换为logits
            toxigen_logits = np.log(toxigen_pred + 1e-10)
            hurtlex_logits = np.log(hurtlex_pred + 1e-10)
            
            # 更新校准
            self.confidence_calibrator.update_calibration(
                toxigen_logits, hurtlex_logits, true_label, performance_metrics
            )
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        stats = self.detection_stats.copy()
        
        # 添加子检测器统计
        if self.confidence_calibrator:
            stats['calibration_stats'] = self.confidence_calibrator.get_calibration_stats()
        
        if self.entropy_detector:
            stats['entropy_stats'] = self.entropy_detector.get_analysis_statistics()
        
        return stats
    
    def save_detection_data(self, base_path: str = "./enhanced_detection_data"):
        """保存检测数据"""
        import os
        os.makedirs(base_path, exist_ok=True)
        
        if self.confidence_calibrator:
            cal_path = os.path.join(base_path, "calibration_data.json")
            self.confidence_calibrator.save_calibration_data(cal_path)
        
        if self.entropy_detector:
            ent_path = os.path.join(base_path, "entropy_data.json")
            self.entropy_detector.save_analysis_data(ent_path)
        
        # 保存统计信息
        import json
        stats_path = os.path.join(base_path, "detection_stats.json")
        try:
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(self.detection_stats, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存检测统计失败: {e}")
    
    def load_detection_data(self, base_path: str = "./enhanced_detection_data"):
        """加载检测数据"""
        import os
        import json
        
        if self.confidence_calibrator:
            cal_path = os.path.join(base_path, "calibration_data.json")
            if os.path.exists(cal_path):
                self.confidence_calibrator.load_calibration_data(cal_path)
        
        if self.entropy_detector:
            ent_path = os.path.join(base_path, "entropy_data.json")
            if os.path.exists(ent_path):
                self.entropy_detector.load_analysis_data(ent_path)
        
        # 加载统计信息
        stats_path = os.path.join(base_path, "detection_stats.json")
        if os.path.exists(stats_path):
            try:
                with open(stats_path, 'r', encoding='utf-8') as f:
                    self.detection_stats = json.load(f)
            except Exception as e:
                logger.error(f"加载检测统计失败: {e}")
