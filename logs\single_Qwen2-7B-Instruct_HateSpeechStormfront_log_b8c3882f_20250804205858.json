{"metadata": {"run_id": "b8c3882f", "dataset": "HateSpeechStormfront", "model": "Qwen2-7B-Instruct", "detector_type": "SingleAgentDetector", "system_type": "single_agent", "num_samples": 478, "start_idx": 0, "created_at": "2025-08-04T21:06:22.410506"}, "metrics": {"accuracy": 0.803347280334728, "precision": 0.8008298755186722, "recall": 0.8075313807531381, "f1": 0.8041666666666667, "confusion_matrix": [[191, 48], [46, 193]], "avg_processing_time": 0.9287753125114919}, "results": [{"verdict": 1, "explanation": "Contains racist and xenophobic content, suggesting exclusion based on race and immigration status.", "processing_time": 7.230753183364868, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and xenophobic content, suggesting exclusion based on race and immigration status.\"\n}", "text": "The white man has no future in Canada , that is for sure , because the country is being flooded with non-white immigrants .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting content towards a nation", "processing_time": 0.7636797428131104, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting content towards a nation\"\n}", "text": "I take the assumption that your Nation is so crap is not worth remembering .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'black intellectual' and mocking tone indicated by 'lol'", "processing_time": 0.9314901828765869, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'black intellectual' and mocking tone indicated by 'lol'\"\n}", "text": "If you want to hear what a black `` intellectual '' ( lol ) sounds like , check out the woman in this video trying to debate Dr. <PERSON> .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses a personal opinion about children from a certain neighborhood without using explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.1749660968780518, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses a personal opinion about children from a certain neighborhood without using explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "I 've always considered teaching as one of the professions I would like to get into , but not in a neighbourhood like that ... never. kids like that disgust me .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9698600769042969, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "And the sad thing is the white students at those schools will act like that too .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'Jewish supremacist' implying bias and control", "processing_time": 0.8850572109222412, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'Jewish supremacist' implying bias and control\"\n}", "text": "And my job lets me monitor all of Jewish supremacist control of talk radio .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a link to a neo-Nazi forum, indicating potential hate speech or extremism", "processing_time": 0.9207549095153809, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a link to a neo-Nazi forum, indicating potential hate speech or extremism\"\n}", "text": "See the new thread @ https : //www.stormfront.org/forum/t989503/", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain any offensive speech; it simply expresses an opinion.", "processing_time": 0.9294044971466064, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain any offensive speech; it simply expresses an opinion.\"\n}", "text": "I read a few things about him awhile back and he is far to the left as so many of them are .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9810056686401367, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "This whole situation starts to become really bizarre .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and racial slurs", "processing_time": 0.7951483726501465, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and racial slurs\"\n}", "text": "They need the white male distracted as much as possible and of course the glorification of the groid ; two birds one stone .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful content targeting a specific group", "processing_time": 0.8094253540039062, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful content targeting a specific group\"\n}", "text": "I will at least never willingly pay to see this propaganda , maybe just watch it for free to show the influence the jews have got in the white genocide to people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses a concern about limited space, without containing offensive content.", "processing_time": 0.8739414215087891, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses a concern about limited space, without containing offensive content.\"\n}", "text": "Pretty soon , there 's not going to be anywhere left to run .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and promotes hatred towards a specific group", "processing_time": 0.82855224609375, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and promotes hatred towards a specific group\"\n}", "text": "I reported some time ago that negroes will even strip the bricks off your home while you are away at work .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and blasphemy towards a religious figure", "processing_time": 0.8307287693023682, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and blasphemy towards a religious figure\"\n}", "text": "This family that wants to complain about gays should come to San Francisco and raise some hell at these gay pride events .... a much better target ..... and these people in San Francisco openly blasphemied <PERSON> with their parody of ' The Last Supper `` as their poster for their last event .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and discriminatory content", "processing_time": 0.6753489971160889, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and discriminatory content\"\n}", "text": "That or it might be that Blacks have smaller brains and an IQ 15-30 points lower than that of White people on average .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuation of harm towards specific groups", "processing_time": 0.8879456520080566, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuation of harm towards specific groups\"\n}", "text": "We have two things wrong here , Mexcicans in a White homeland and a Pizza company sending a female in harms way .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9983160495758057, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "How much longer are we going to wait for them to take care of themselves ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any offensive content.", "processing_time": 0.7627017498016357, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any offensive content.\"\n}", "text": "I hear that <PERSON><PERSON> is coming to their aid .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0014801025390625, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "That's all I needed to hear .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory statement targeting a racial group", "processing_time": 0.8063490390777588, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory statement targeting a racial group\"\n}", "text": "Blacks hate whites .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and hate speech targeting a specific group", "processing_time": 0.8662309646606445, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and hate speech targeting a specific group\"\n}", "text": "The media wants to bury the story because they care more about homo killers not getting their sick fetish for young boys fufilled .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement uses a metaphorical comparison without explicit insult, threat, hatred, or derogation.", "processing_time": 1.043473243713379, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement uses a metaphorical comparison without explicit insult, threat, hatred, or derogation.\"\n}", "text": "The white churches ' billboards look like lunch break at the UN .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist language and implies justification for harm towards a racial group", "processing_time": 0.8876004219055176, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist language and implies justification for harm towards a racial group\"\n}", "text": "I'm sure the negro had it coming .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening content towards Whites", "processing_time": 0.7593772411346436, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening content towards Whites\"\n}", "text": "ZOG is getting rid of every White-dominated area in the world .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and explicit content directed towards a specific individual", "processing_time": 0.8406810760498047, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and explicit content directed towards a specific individual\"\n}", "text": "Except , of course , for those that pander to the filthy sons and daughters of <PERSON> like ' ol <PERSON> here .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Insults directed towards a specific group (monkeys)", "processing_time": 0.8387722969055176, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Insults directed towards a specific group (monkeys)\"\n}", "text": "What did those poor monkeys ever do to you ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insult directed at someone", "processing_time": 0.7107212543487549, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insult directed at someone\"\n}", "text": "Do ` F * ck with our families , buckwheat !!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and violent imagery directed towards specific countries and their people", "processing_time": 0.8766293525695801, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and violent imagery directed towards specific countries and their people\"\n}", "text": "I always thought it would be cool if somebody covertly set off a nuke in Kashmir , and then we all could sit back and watch as the Paki 's and Indians nuke the scheiss out of each other .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and discriminatory language", "processing_time": 0.6908538341522217, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and discriminatory language\"\n}", "text": "If it 's black , send it back .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and insinuation of violence against a group", "processing_time": 0.8951930999755859, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and insinuation of violence against a group\"\n}", "text": "May they just need to turn these savages loose , justice will take care of itself .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language towards <PERSON><PERSON><PERSON> and her children", "processing_time": 0.899545431137085, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language towards <PERSON><PERSON><PERSON> and her children\"\n}", "text": "Just let <PERSON><PERSON><PERSON> leave her kids home to burn the house down while she goes to da club .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'hood' implying negative connotations about a neighborhood, potentially fostering fear and discrimination", "processing_time": 1.1117603778839111, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'hood' implying negative connotations about a neighborhood, potentially fostering fear and discrimination\"\n}", "text": "Then we will see who is afraid to leave thier homes after dark or venture into the wrong ' hood .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears neutral, expressing a fact without explicit offense.", "processing_time": 0.8664779663085938, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears neutral, expressing a fact without explicit offense.\"\n}", "text": "I mean they intentionally chose this house knowing there were white people living there .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains implicit criticism and questioning of legal outcomes, potentially implying wrongdoing without explicit evidence", "processing_time": 0.9669990539550781, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains implicit criticism and questioning of legal outcomes, potentially implying wrongdoing without explicit evidence\"\n}", "text": "Excellent Article !! * * * * * Why Were n't They In Jail ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit content related to violence and victimization, targeting an individual.", "processing_time": 0.8953354358673096, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit content related to violence and victimization, targeting an individual.\"\n}", "text": "They do not want all of us to find out how she was raped and tortured .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains implicit threat and derogatory language towards a group", "processing_time": 0.849895715713501, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains implicit threat and derogatory language towards a group\"\n}", "text": "A public execution for these animals would go a LONG way to point to justice being done ; it would never bring the girl back ; but I guarantee it would send a message ............................", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains implicit threat and derogatory term 'thugs' directed towards a group", "processing_time": 0.9540996551513672, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains implicit threat and derogatory term 'thugs' directed towards a group\"\n}", "text": "God please bless with wisdom the judge and jury against this group of thugs that they all might be hanged .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to express an opinion rather than containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1109204292297363, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to express an opinion rather than containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "It seems that what they are trying to say is that if you are white you are in the wrong place at the wrong time anywhere in the World .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and insinuates negative stereotypes, potentially inciting hatred.", "processing_time": 0.9399049282073975, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and insinuates negative stereotypes, potentially inciting hatred.\"\n}", "text": "Nevermind, found the new page Knew it had to be a groid .... any report of rape ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and threats directed towards unspecified individuals", "processing_time": 0.78080153465271, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and threats directed towards unspecified individuals\"\n}", "text": "Perhaps I should walk back into my H.S. when it starts Monday and get on the P.A. system and say I want to tie all * * * * * * s to the back of my car and drag them across the ground till they die , and then say I 'm joking .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language towards a group", "processing_time": 0.7946157455444336, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language towards a group\"\n}", "text": "Let mother nature evolve these people naturally because no amount of money or aid is going to drag these ' things ' into the civilized world where they serve absolutely no purpose .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9592680931091309, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Right.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial slurs and derogatory language targeting a specific group", "processing_time": 0.9174835681915283, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial slurs and derogatory language targeting a specific group\"\n}", "text": "I'm heading down to DC to put a sign on the White House gates that will read : ' No savage negro BEASTS in the White House ! '", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.890650749206543, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "http://www.amv3.com/forum/viewtopic.php ? p = 5542#5542", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive content.", "processing_time": 0.7648484706878662, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive content.\"\n}", "text": "Great link .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain any offensive content; it's a mere opinion.", "processing_time": 0.9351449012756348, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain any offensive content; it's a mere opinion.\"\n}", "text": "It almost reads like something out of a science fiction novel .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and derogatory language targeting race.", "processing_time": 0.7952330112457275, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and derogatory language targeting race.\"\n}", "text": "He's a retarded feral negro ( yes , feral humans who cannot talk occasionally pop up , having been abandoned as children. ) .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.994121789932251, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "They do what they always do take something of someone else 's and make it their own .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and stereotypical content targeting racial groups", "processing_time": 0.8586530685424805, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and stereotypical content targeting racial groups\"\n}", "text": "No doubt they 'll be coming up with chocolate-colored Leprechauns and a Chinese flavored Uncle <PERSON> 's too .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text appears to describe personality traits and preferences without containing offensive content.", "processing_time": 0.9131720066070557, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text appears to describe personality traits and preferences without containing offensive content.\"\n}", "text": "INTJ Introvert ( 11 % ) iNtuitive ( 38 % ) iNtuitive Thinking ( 38 % ) Judging ( 22 % ) You have slight preference of Introversion over Extraversion ( 11 % ) You have moderate preference of Intuition over Sensing ( 38 % ) You have moderate preference of Thinking over Feeling ( 38 % ) You have slight preference of Judging over Perceiving ( 22 % )", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement appears to be a non-offensive expression of a numerical score.", "processing_time": 0.9156150817871094, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement appears to be a non-offensive expression of a numerical score.\"\n}", "text": "Only scored a 120 .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful content directed towards a specific group, the Jews.", "processing_time": 0.9364809989929199, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful content directed towards a specific group, the Jews.\"\n}", "text": "In Eternal Combat with that Satanic Spirit - The physical incarnation , creation of the Demiurge : The Jews .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting Chinese people", "processing_time": 0.7783081531524658, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting Chinese people\"\n}", "text": "This is ARKANSAS they have no right to smell up my place with their chinese restaurant BO ~ ▲ RESTORE ORDER ▲ ~", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.001051425933838, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I do n't think anyone is insinuating that we are equal to non whites , or that we would ignore white nations .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to express an opinion rather than containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1309025287628174, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to express an opinion rather than containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "These people have no idea about who they are or where they came from and any knowledge that they do have has guilt attached to it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.", "processing_time": 0.9880425930023193, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.\"\n}", "text": "It 's a nationalist site as well , maybe give them a try !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content detected; page appears to be a regular home page.", "processing_time": 0.9043116569519043, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content detected; page appears to be a regular home page.\"\n}", "text": "Invictus Press Home Page", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.8796720504760742, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "Pure .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Suggests a discriminatory context by implying race is relevant without providing further information", "processing_time": 0.9523382186889648, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Suggests a discriminatory context by implying race is relevant without providing further information\"\n}", "text": "It has everything to do with RACE .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9948229789733887, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I think it is a bit late to think to look after the safety and the future of white people in South africa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement expresses a desire for online activism to address injustice without containing offensive content.", "processing_time": 0.965001106262207, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement expresses a desire for online activism to address injustice without containing offensive content.\"\n}", "text": "Or make something in the internet against thi injustice some times is hit a global repercussion and can help this .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and derogatory language targeting a specific group", "processing_time": 0.8549070358276367, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and derogatory language targeting a specific group\"\n}", "text": "WE GOT MONEY AROUND HERE TOO Compton Crips Pt.1 - YouTube These negroes are in the middle of the street , seems like something of a zoo , playing with their cheap lowriders .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0112214088439941, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "My favorite part : And still he ca n't see the truth right in front of his face .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and expresses hatred towards a specific group", "processing_time": 0.8321876525878906, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and expresses hatred towards a specific group\"\n}", "text": "I know exactly what you 're saying , I just hate to see those worthless apes benefit from the great things that whites do .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language directed at unspecified individuals", "processing_time": 0.7688908576965332, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language directed at unspecified individuals\"\n}", "text": "Seems none of these pinheads wanted to get dirty .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and promotes aggression based on sexual orientation and race", "processing_time": 0.901212215423584, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and promotes aggression based on sexual orientation and race\"\n}", "text": "It 's going viral on Youtube - warning the lesbian ' groid gets aggressive on the whites for whatever reason : YouTube - Video Caught On Tape Lesbian Or Young Man Going Wild On BART Train In Oakland !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist language targeting a specific group", "processing_time": 0.8231313228607178, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist language targeting a specific group\"\n}", "text": "More impulsive Negro stripping ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement is an opinion about video content and does not contain offensive speech.", "processing_time": 0.9398674964904785, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement is an opinion about video content and does not contain offensive speech.\"\n}", "text": "Most of these videos belong on the Animal Planet tv channel .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting a specific group", "processing_time": 0.8351840972900391, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting a specific group\"\n}", "text": "The kind of savagery against females that has become expected from negroids in todays society ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory context regarding a specific group (blind women)", "processing_time": 0.8534722328186035, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory context regarding a specific group (blind women)\"\n}", "text": "Seattle bus attack on a blind woman : YouTube - Broadcast Yourself .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting Islam and religious groups", "processing_time": 0.8543100357055664, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting Islam and religious groups\"\n}", "text": "The islamic disease is even more dangerous than the more obvious black or yellow disease .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implications and stereotypes about Chinese men and suggests predatory behavior towards women.", "processing_time": 0.9752190113067627, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implications and stereotypes about Chinese men and suggests predatory behavior towards women.\"\n}", "text": "Maybe they will be resettled deliberately as refugees or maybe they will come for the women like the chinese men are doing .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about the frequency of race mixing ads on TV without containing offensive content.", "processing_time": 1.055440902709961, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about the frequency of race mixing ads on TV without containing offensive content.\"\n}", "text": "There are so many race mixing ads on TV that it gets overwhelming .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text uses racial stereotypes and makes comparisons between humans and monkeys, which can be considered derogatory.", "processing_time": 1.048828363418579, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text uses racial stereotypes and makes comparisons between humans and monkeys, which can be considered derogatory.\"\n}", "text": "When I was last time in South Africa , we went to Sun City and saw little black monkey children swimming , funniest thing was watching drying water off their faces , it looks exactly like baboon or something , way they do it , whites have certain mannerisms with doing things , they have completely ape-like ones .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and expressions of hatred towards a specific group.", "processing_time": 0.8888771533966064, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and expressions of hatred towards a specific group.\"\n}", "text": "The jew media likes to prop this whites becoming the minority non sense up because they want to try and scare whitey , the media plays on the fears that the average retard thinks hispanic or latino is a race and they play word play with titles like white hispanic and white non hispanic it is all a magic show nothing more .. But Whites becoming a minority in Europe the homeland is a much more serious issue that concerns me greatly .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term'subhumans' implying hatred towards a group", "processing_time": 0.9286575317382812, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term'subhumans' implying hatred towards a group\"\n}", "text": "The future is ours , as long as we do not sit by and wait to be overrun by the subhumans .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implication towards ethnic groups and promotes negative stereotypes", "processing_time": 0.8548910617828369, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implication towards ethnic groups and promotes negative stereotypes\"\n}", "text": "We all know that when their are too many ethnics , the neighbourhood soon turn 's into a slum .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text links to a blog containing 'black-on-white' hate crime videos, implying racial bias and negativity.", "processing_time": 1.104379653930664, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text links to a blog containing 'black-on-white' hate crime videos, implying racial bias and negativity.\"\n}", "text": "More black-on-white hate crime videos are posted here : http://ethniccrime.blogspot.com/p/ra...-on-video.html", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language towards a specific group", "processing_time": 0.8310184478759766, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language towards a specific group\"\n}", "text": "I have a dream - one of these days Black occupation of America will be history !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit calls for violence and death against a specific group", "processing_time": 0.846668004989624, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit calls for violence and death against a specific group\"\n}", "text": "There is nothing I would love to see more than the arrest , trial and execution of these murderous and genocidal Zionists !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting language directed towards a group", "processing_time": 0.8146882057189941, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting language directed towards a group\"\n}", "text": "Pathological killers , they are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a historical stereotype and accusation directed against a specific group", "processing_time": 0.8562941551208496, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a historical stereotype and accusation directed against a specific group\"\n}", "text": "Jews have been poisoning wells since Biblical times .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains threatening and derogatory content towards an unspecified entity", "processing_time": 0.8519628047943115, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains threatening and derogatory content towards an unspecified entity\"\n}", "text": "All you need is to cut off it 's head and the rest of the problems will crumble away with ease", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains graphic details suggesting violence against an individual", "processing_time": 0.76755690574646, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains graphic details suggesting violence against an individual\"\n}", "text": "She was tortured and mutilated .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and derogatory language targeting a specific group.", "processing_time": 0.8865675926208496, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and derogatory language targeting a specific group.\"\n}", "text": "God, it is sickening to see these young lives taken by feral negroes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content directed at a specific group", "processing_time": 0.8599057197570801, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content directed at a specific group\"\n}", "text": "God bless them all , to hell with the blacks .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content targeting a specific racial group", "processing_time": 0.857330322265625, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content targeting a specific racial group\"\n}", "text": "Whites have the following Too many abortions , too many mixed marriages , too many no kids marriages , and too many liberals .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial stereotypes and derogatory implications", "processing_time": 0.7398624420166016, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial stereotypes and derogatory implications\"\n}", "text": "- `` Something about black '' and the old favorite `` Once you go black you never go back ''", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuation of racial bias", "processing_time": 0.8509767055511475, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuation of racial bias\"\n}", "text": "Their stories have been told before , so instead of showing the pics of the non WHITE garbage that took from them , I am just going to post their pics so they will not be forgotten .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term and implies unfair treatment towards a specific group", "processing_time": 0.8667311668395996, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term and implies unfair treatment towards a specific group\"\n}", "text": "While they are in prison the kikes should get the same food the regular prison population gets ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and derogatory language targeting a specific group.", "processing_time": 0.8457283973693848, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and derogatory language targeting a specific group.\"\n}", "text": "Only a f' n jew would have the nerve to hide away in a black country , a fugitive from US law , while making campaign promises at the same time as suing the company he defrauded .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and dehumanizing language targeting racial groups", "processing_time": 0.7856860160827637, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and dehumanizing language targeting racial groups\"\n}", "text": "Why do n't White Europeans and Americans grow a set of balls , and deport these Black and brown animals from their countries ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content directed towards a specific group.", "processing_time": 0.875615119934082, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content directed towards a specific group.\"\n}", "text": "I 'm angry against myself for just letting this whole world drown into a jewish cesspool .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting language towards a specific individual and group, including hate speech.", "processing_time": 0.967747688293457, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting language towards a specific individual and group, including hate speech.\"\n}", "text": "somebody``hcf''calledputin``ahero''!lololol!This``hero''finallytransformedformersovietjewnion in ``russian''judea!In this misery-torn country-are there-70 officially billionaries , tens f thousands millionaries , and tens of millions of poor and miserable who look for their pittance at the garbage places !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement compares ethnicities without expressing offense, hatred, or discrimination.", "processing_time": 0.8905847072601318, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement compares ethnicities without expressing offense, hatred, or discrimination.\"\n}", "text": "Comparing these pictures to pictures with serbs I do n't see much difference between them and albanians , the same Dinaric and Mediterranean subtypes .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expression of personal dislike and pride in cultural heritage; no explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1299583911895752, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expression of personal dislike and pride in cultural heritage; no explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "nobody said I like him and I dont see the reason to like him to be honest WE HAVE ENOUGH CULTURAL HERITAGE FOR HUNDREDS OF GENERATIONS WE SHOULD BE PROUD OF WHAT WE DID NOT OF WHAT SOME AUSTRIAN DID !!!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'kikes' and promotes hatred towards Palestinians.", "processing_time": 0.8857576847076416, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'kikes' and promotes hatred towards Palestinians.\"\n}", "text": "Those poor kikes they cant even escape antisemetism in the country were it is punishable by death ( I am talking about palestinians )", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, just a question regarding political matters.", "processing_time": 0.9490702152252197, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, just a question regarding political matters.\"\n}", "text": "I thought there was talk not too long ago about <PERSON> extending the terms to stay in office ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement uses a metaphor comparing something to a Trabant car, which could be seen as dismissive but does not explicitly insult or threaten.", "processing_time": 1.2461705207824707, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement uses a metaphor comparing something to a Trabant car, which could be seen as dismissive but does not explicitly insult or threaten.\"\n}", "text": "They 're probably as high-maintenance as one of those crappy old East German Trabants , though .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting language targeting religious and ethnic groups", "processing_time": 0.8482403755187988, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting language targeting religious and ethnic groups\"\n}", "text": "I could never picture the site of one of the women of the Holy Rus shacking up with some Islamic mud from that fithy , impoverished , and godforsaken mountainous * * * * hole !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement presents demographic information without expressing offense.", "processing_time": 0.7401397228240967, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement presents demographic information without expressing offense.\"\n}", "text": "Slavic peoples make up some 34 % of European population .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit details about child abuse and death, potentially triggering and distressing.", "processing_time": 0.9473989009857178, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit details about child abuse and death, potentially triggering and distressing.\"\n}", "text": "2-Year-Old <PERSON><PERSON> Of Rape , <PERSON><PERSON> 's <PERSON><PERSON><PERSON><PERSON> ' Charged - NNN Reporters Newsroom Forum Dekalb 2-Year-Old Dead ; Caregiver Charged Police Say Child Was Abused POSTED : 10 : 41 pm EDT October 2 , 2010 UPDATED : 11 : 51 pm EDT October 3 , 2010 Dekalb 2-Year-Old Dead ; Caregiver Charged - DeKalb County Headlines News Story - WGCL Atlanta", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "News report detailing an incident without expressing offensive speech", "processing_time": 0.791968584060669, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"News report detailing an incident without expressing offensive speech\"\n}", "text": "Richland County deputies say <PERSON><PERSON><PERSON> killed his estranged wife , 38-year-old <PERSON><PERSON><PERSON> , at an apartment on Smallwood Road in Northeast Richland County Deputies : Murder Suspect Shoots at Officers , is Taken Into Custody wltx.com | Columbia , SC News , Weather and Sports |", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial discrimination and exclusionary language", "processing_time": 0.7379693984985352, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial discrimination and exclusionary language\"\n}", "text": "Keep Russia free from dark skinned beings !!!! thank you and please forgive me for mistakes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and violent content suggesting harm towards oneself, which can be interpreted as offensive.", "processing_time": 0.9846634864807129, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and violent content suggesting harm towards oneself, which can be interpreted as offensive.\"\n}", "text": "They will kill there own just as fast as they would kill there enemy .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9895048141479492, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "14 !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9686613082885742, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Oh yeah , no mugshot picture on the page of course , you have to watch the video report to find out it was yet another case of TNB .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.", "processing_time": 0.9521262645721436, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.\"\n}", "text": "The brutality of his crime and his lack of remorse made <PERSON> the `` model candidate for death in the gas house , '' one columnist wrote after his death sentence was upheld by the state 's highest court .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racism and hatred directed towards a specific group.", "processing_time": 0.8215501308441162, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racism and hatred directed towards a specific group.\"\n}", "text": "YouTube - Teacher Attacked for Being White 9 year old attacks white teacher , explicitly `` because she was white . ''", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.8654139041900635, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "I caught this on youtube , I 'm sure someone has posted this at some point on here but it deserves reiteration .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains gender and racial stereotypes and implied physical aggression", "processing_time": 0.7771461009979248, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains gender and racial stereotypes and implied physical aggression\"\n}", "text": "I do n't know about you but I rather fight a 2m swedish male than a 1.70m asian kung fu expert .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The phrase 'young man' is a respectful address and does not contain offensive content.", "processing_time": 0.9550261497497559, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The phrase 'young man' is a respectful address and does not contain offensive content.\"\n}", "text": "Now, young man !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The input does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9707140922546387, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The input does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "2508", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting a specific group based on ethnicity.", "processing_time": 0.870161771774292, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting a specific group based on ethnicity.\"\n}", "text": "So you would refuse even other Nordic whites fleeing the brown chaos in the south ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.973311185836792, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Interesting article about what to do with these evil persons who keep telling the truth and who don ´ t want to surrender ( in Danish )", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is neutral, discussing safety of food items without any offensive content.", "processing_time": 0.9033863544464111, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is neutral, discussing safety of food items without any offensive content.\"\n}", "text": "Sardines and anchovies , for example , are basically 100 % safe .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'Jew' implying bias and discrimination against Jewish people", "processing_time": 0.940788745880127, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'Jew' implying bias and discrimination against Jewish people\"\n}", "text": "That has been proven many times over by the Jew media in the U.S !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement criticizes someone's dishonesty without expressing hatred, threats, or derogatory language.", "processing_time": 1.03609299659729, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement criticizes someone's dishonesty without expressing hatred, threats, or derogatory language.\"\n}", "text": "Their lies are so thick and many that people do n't see the truth when it is in front of them !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This sentence does not contain offensive content; it appears to be asking about different types of workers.", "processing_time": 1.034397840499878, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This sentence does not contain offensive content; it appears to be asking about different types of workers.\"\n}", "text": "Servents/maids , workers of some type ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9742476940155029, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Aragorn", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuation of racial discrimination", "processing_time": 0.8408570289611816, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuation of racial discrimination\"\n}", "text": "I would like to say and I can proudly say that there are no jews in Iceland , those that tried to get here were all sent back to Germany ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is a neutral request for information without any offensive content.", "processing_time": 0.8604285717010498, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is a neutral request for information without any offensive content.\"\n}", "text": "If anyone can tell what happened on this lake ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and implies violence against a specific group", "processing_time": 0.8560855388641357, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and implies violence against a specific group\"\n}", "text": "When the homos tried to have a fag pride parade in Serbia a few years ago they were beaten up .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content directed at a group", "processing_time": 0.764350414276123, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content directed at a group\"\n}", "text": "Idiotic.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9903786182403564, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "But as for them adopting kids , the answer is NO .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses concern about ethnic crime without directing insults, threats, hatred, or derogatory content.", "processing_time": 1.0559868812561035, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses concern about ethnic crime without directing insults, threats, hatred, or derogatory content.\"\n}", "text": "Ethnic crime never ends it seems .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text presents demographic data without containing offensive speech.", "processing_time": 0.8357176780700684, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text presents demographic data without containing offensive speech.\"\n}", "text": "Norway : 3.3 ( 2001 ) , 3.9 ( 2002 ) , 3.5 ( 2003 ) , 1.8 ( 2004 ) . ( assuming population of 4.5 million ) Sweden : 2.6 ( 2001 ) , 3.7 ( 2002 ) , 3.5 ( 2003 ) , 2.6 ( 2004 ) . ( assuming population of 8.9 million ) Iceland is at about 0.2 .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content directed towards a religious group", "processing_time": 0.8058774471282959, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content directed towards a religious group\"\n}", "text": "It's people like this that are the face that represents the filth of Islam .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to express annoyance but lacks explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1001014709472656, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to express annoyance but lacks explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Providing From Finland does n't kick my ass and throw me overboard ... coming or going .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears neutral, expressing a perspective on people's feelings over time.", "processing_time": 0.9464833736419678, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears neutral, expressing a perspective on people's feelings over time.\"\n}", "text": "Some of them claim that they would like to go back home one day but after a few years in Finland they will no longer have this wish .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting a specific ethnicity and cultural practice.", "processing_time": 0.9060788154602051, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting a specific ethnicity and cultural practice.\"\n}", "text": "that prince who married that as<PERSON> is a freak. imagine his half and half kids growing up and trying to marry into other royal families", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0104162693023682, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "<PERSON> 278", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0085625648498535, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "It 's more fun that way , too .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and stereotypes directed towards a specific group, including insinuations of legal issues related to marriage.", "processing_time": 1.1472983360290527, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and stereotypes directed towards a specific group, including insinuations of legal issues related to marriage.\"\n}", "text": "More likely they 're afraid of having to marry and deal with a snotty GI-Jane-dyke , only to be dragged into court and have the kids and house taken a few years later .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain offensive content; it appears to be a question about Finnish history.", "processing_time": 1.0342462062835693, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain offensive content; it appears to be a question about Finnish history.\"\n}", "text": "Has anybody pointed out that Finland is n't part of Scandinavia yet ? ( <PERSON>r gick det i Finnkampen , vet ni det ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about future conditions without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1392717361450195, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about future conditions without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Its only a matter of time until we become a hell hole like Sweden or the UK .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit threat and profanity", "processing_time": 0.7098698616027832, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit threat and profanity\"\n}", "text": "Piece of * * * * deserves to be shot .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses curiosity about someone's appearance without using explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.1601076126098633, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses curiosity about someone's appearance without using explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "now that ye mention it he does look a bit like this traveller i knew. i wonder if we gave him a wash would he really be a white guy with red hair ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0144999027252197, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "If we do n't act soon they will bread us out .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory name and implies harmful intent towards a group", "processing_time": 0.8602125644683838, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory name and implies harmful intent towards a group\"\n}", "text": "And meanwhile , <PERSON> is trying to kill off future generations of Irish .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language directed towards a group", "processing_time": 0.8429925441741943, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language directed towards a group\"\n}", "text": "On a short holiday , Go back and do something about your own country and stop putting crap on our doorstep !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.036750316619873, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Hey, do you think we are going to get into a fight between European countries about who all are going to get those Guantanamo prisoners ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist stereotypes and promotes discrimination against specific ethnic groups", "processing_time": 0.8361918926239014, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist stereotypes and promotes discrimination against specific ethnic groups\"\n}", "text": "I heard from friends in Boston this is true.Blacks will attack an Irish America with a shamrock tattoo.How long before this happens in Ireland ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and discriminatory content", "processing_time": 0.7085633277893066, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and discriminatory content\"\n}", "text": "So according to your logic since blacks have been in N.America for hundreds of yrs they should be allowed to stay ? and yes whites should leave africa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.009124994277954, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "If they have some marches and what not about showing pride in their culture you guys should go out and do the same If you could try and contact people you know in different cities and see if their willing to do the same .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text expresses prejudice and stereotypes about immigrants, suggesting they lack skills and implies they are from a backward country.", "processing_time": 1.1169464588165283, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text expresses prejudice and stereotypes about immigrants, suggesting they lack skills and implies they are from a backward country.\"\n}", "text": "how the hell are they going to integrate into Irish society ? They come from a dirt poor medieval country and have spent the last 3 years living in a refugee camp.what skills are they going to bring here ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement criticizes the perceived leniency of the Irish justice system but does not contain offensive language.", "processing_time": 1.0936813354492188, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement criticizes the perceived leniency of the Irish justice system but does not contain offensive language.\"\n}", "text": "An Irish prison is like a luxury hotel where they come and thats even if they get sent to prison and thats if they get do n't flee the country and thats if they get caught doing the crime ... so crime does pay in Ireland .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuation of hatred towards a specific group", "processing_time": 0.9393937587738037, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuation of hatred towards a specific group\"\n}", "text": "Israel arrests him i bet <PERSON><PERSON> and <PERSON> are very happy.All students looking in see what scum the Jews are.Details here www.normanfinkelstein.com", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and insults targeting political beliefs and cultural groups", "processing_time": 0.8894553184509277, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and insults targeting political beliefs and cultural groups\"\n}", "text": "The red scum will never wake up , ignorant lefties fight for `` their class '' when their making a mockery of their culture and heritage . commie scum ..", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language towards a specific group based on nationality", "processing_time": 0.930098295211792, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language towards a specific group based on nationality\"\n}", "text": "Hopefully they will get deportation orders and be on the next flight back to Cairo .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "A statement expressing a desire for a house, no offensive content.", "processing_time": 0.9000957012176514, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"A statement expressing a desire for a house, no offensive content.\"\n}", "text": "I would love a house .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and disrespect towards someone implied by 'this creature'", "processing_time": 0.9143514633178711, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and disrespect towards someone implied by 'this creature'\"\n}", "text": "To hell with what this creature thinks anyway .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains implicit threat and derogatory implication towards a group", "processing_time": 0.8573088645935059, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains implicit threat and derogatory implication towards a group\"\n}", "text": "The problems start when you start to accommonadate these people in the first place ! , give an inch and they 'll take a mile ......", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory remarks and threats directed towards a religious group and uses racial insinuations", "processing_time": 1.0221123695373535, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory remarks and threats directed towards a religious group and uses racial insinuations\"\n}", "text": "Tralee is a disgrace , the place looks like a town in africa nowThe day Ireland becomes a muslim country is the day i stop breathing !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to advocate for political causes without explicitly containing offensive language.", "processing_time": 0.9371354579925537, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to advocate for political causes without explicitly containing offensive language.\"\n}", "text": "YouTube - Brussels on 9/11 : For a free Europe , against Islamization", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language against foreigners", "processing_time": 0.7821495532989502, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language against foreigners\"\n}", "text": "Its disgusting they these foreigners come over here , stamp their feet and jump above good Irish families and get what they want .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.018092393875122, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Even if they do stop pouring in most of those that are here have no intention of going back to poland .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text describes fashion choices and does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1090233325958252, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text describes fashion choices and does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "During the early punk scene , he was always seen decked out in a Union flag blazer , then all a sudden , an instant nationality transplant .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory comparison targeting a specific individual and nationality", "processing_time": 0.8239090442657471, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory comparison targeting a specific individual and nationality\"\n}", "text": "When I look at the Zimbawee 's Mugabe , I think `` Ape '' or `` Gorilla '' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains an ethnic slur and an implied threat", "processing_time": 0.7609477043151855, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains an ethnic slur and an implied threat\"\n}", "text": "Back to where ya came from !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses a wish for someone's recovery and acknowledges good fortune without explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.2212779521942139, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses a wish for someone's recovery and acknowledges good fortune without explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "You got that right in one go.Lets hope he gets well soon and on his way home If you do n't have these apes in your great Country then count your blessings", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0035207271575928, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "After knocking back a few guinness we got pretty hungry and he said he knew a great place to go get some food , i was disgusted when he brought me to a chinese restaurant .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and xenophobic content, expressing fear and hostility towards immigrants and minorities.", "processing_time": 0.9869005680084229, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and xenophobic content, expressing fear and hostility towards immigrants and minorities.\"\n}", "text": "the white race will soon be on the brink of being a minority in a few years if nothing is done to stop these vile hordes invading our lands , I for one do not want to live in a city like Bradford or Birmingham where we are outnumbered 2 to 1 or worse", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and insinuation of corruption", "processing_time": 0.8183395862579346, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and insinuation of corruption\"\n}", "text": "Its a disgrace my sister has been on the waiting list for 6 years and their giving away houses to these scumbags", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and references to violence, which indicate offensive content", "processing_time": 0.9463677406311035, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and references to violence, which indicate offensive content\"\n}", "text": "I heard about it is it about that white guy that killed that race traitor slut and her nig nog boyfriend ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implications and criticism towards a group based on their perceived actions regarding foreign aid and social welfare", "processing_time": 1.117774248123169, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implications and criticism towards a group based on their perceived actions regarding foreign aid and social welfare\"\n}", "text": "These people who want foreign aid increased are the same people who walk past irish people living rough on the streets and they do n't care about the old people who the government ignores .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory labeling and insinuation of criminality towards a specific group", "processing_time": 0.9649574756622314, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory labeling and insinuation of criminality towards a specific group\"\n}", "text": "It just goes to show that the IRA are just criminals and not freedom fighters as they claim .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text appears to be a friendly greeting with no offensive content.", "processing_time": 0.8630247116088867, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text appears to be a friendly greeting with no offensive content.\"\n}", "text": "<PERSON>tha math dhuibh agus dè tha dol agad fh<PERSON>in , a Phàdruig/Hello Pat , how are you ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text reflects personal nostalgia for past social dynamics without expressing offensive content.", "processing_time": 0.9117789268493652, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text reflects personal nostalgia for past social dynamics without expressing offensive content.\"\n}", "text": "So sick of the way its working , I remember when I was growing up in Winnipeg the little shops were all white runned and owned and would hire people from the community and it was great it helped people and kept streets safe even though me and others would cause some bull * * * * it wasnt anything to how it is now .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement appears to express confusion rather than offensive speech.", "processing_time": 0.8552381992340088, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement appears to express confusion rather than offensive speech.\"\n}", "text": "Now i just dont know what to do or think anymore .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9861035346984863, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "18 times in the last 20 years It 's time for the next generation to fight back to regain our country ..... the baby boomers will lose it for us if we do nothing .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0415058135986328, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "nice to see another maritimer , the south shore is a beautiful place , i ride down that way a lot on my harley , i live in the annapolis valley .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement discusses immigration policy without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0453462600708008, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement discusses immigration policy without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "There's no way to stop them from coming to Canada all that is needed is proof of ownership of a house in India , africa etc and they can enter in on a tourist visa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and targets a specific group", "processing_time": 0.8119223117828369, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and targets a specific group\"\n}", "text": "Should the guy who sang cop killer also have a trial , because he tragted a minority , cops .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0410099029541016, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "It was probably written by a 15 year old .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting specific groups", "processing_time": 0.815753698348999, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting specific groups\"\n}", "text": "I noticed a lot of fat little white girls with monkey boyfriends .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit threat and derogatory content towards a religious group", "processing_time": 0.8679711818695068, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit threat and derogatory content towards a religious group\"\n}", "text": "You can stop this by getting a dead pig and bury it underground where the mosque will be built .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any offensive content, just provides information.", "processing_time": 0.8843655586242676, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any offensive content, just provides information.\"\n}", "text": "I' v checked them out but could n't find much but apparently they have presence in Britain .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implication and discrimination against black players, suggesting they represent 'the worst' rather than showcasing national pride", "processing_time": 1.1797521114349365, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implication and discrimination against black players, suggesting they represent 'the worst' rather than showcasing national pride\"\n}", "text": "Seeing the teams of white nations have black players in their ranks is a depressing site to see.They should be representing the best of our nations not the worst", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text uses derogatory language and makes a threatening comparison, which can be considered offensive.", "processing_time": 1.0374269485473633, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text uses derogatory language and makes a threatening comparison, which can be considered offensive.\"\n}", "text": "Did they put that Groid 15 next to <PERSON> just to make <PERSON> look at least half-human ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text uses derogatory language and implies negative characteristics about immigrants, potentially suggesting they are 'locusts' or 'parasites'. This can be considered offensive.", "processing_time": 1.4970059394836426, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text uses derogatory language and implies negative characteristics about immigrants, potentially suggesting they are 'locusts' or 'parasites'. This can be considered offensive.\"\n}", "text": "What I do know is that I live on a small Island and it seems that a lot of people from various countries in the world are willing to desert their homelands and their families just to come here , in my mind that tells me a lot about said people and their home countries , most of all it highlights their weaknesses of how their own countries have let them down and how they feel the need to feed off somebody elses country , how should we English interpret this without sounding rude ??? , locust ??? , parasite ??? , you tell me .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting African people and implying negative stereotypes", "processing_time": 0.9104924201965332, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting African people and implying negative stereotypes\"\n}", "text": "I just need to turn on a tv and I see it advertised everywhere , mostly African apes with attractive white woman .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech; it's a self-deprecating statement about grammar mistakes.", "processing_time": 1.0726358890533447, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech; it's a self-deprecating statement about grammar mistakes.\"\n}", "text": "I do get my Theirs , theres and they 're , mixed up sometimes but i was educated under Labours rule so i cant be completly to blame .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and explicit insults directed towards a political group", "processing_time": 0.8898224830627441, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and explicit insults directed towards a political group\"\n}", "text": "The Official Website of the British People 's Party A few unpleasant truths re. the zionist ass lickers of the DUP", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting language towards a specific group", "processing_time": 0.8318843841552734, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting language towards a specific group\"\n}", "text": "The problem is that through western elites we have to feed and look after useless savages like malema .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains threat and insult towards an unspecified entity", "processing_time": 0.7795126438140869, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains threat and insult towards an unspecified entity\"\n}", "text": "If that stopped the worthless thing would starve !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.8925917148590088, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "i used to post on here a few years back as kruger but for some reason i cant register with that name so here goes with the new one 88 to all i know and to those i will meet", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "This statement implies support for historical fascist organizations, which can be considered as promoting hatred and derogatory content towards certain ethnic groups.", "processing_time": 1.2195870876312256, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"This statement implies support for historical fascist organizations, which can be considered as promoting hatred and derogatory content towards certain ethnic groups.\"\n}", "text": "Its time for the return of <PERSON><PERSON> 's blackshirts .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is expressing surprise about language diversity in a school setting without any offensive content.", "processing_time": 1.013608455657959, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is expressing surprise about language diversity in a school setting without any offensive content.\"\n}", "text": "I have to say I was shocked when he said the kids at his ( Junior ) school spoke 31 different languages and teachers have had to devise a system of hand signals to communicate !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text discusses societal issues without direct offensive language or targeted hatred.", "processing_time": 0.8902840614318848, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text discusses societal issues without direct offensive language or targeted hatred.\"\n}", "text": "Our country is being over run and people let it happen just because the political issue here seens to take over the racial one .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuation of racial discrimination", "processing_time": 1.1479847431182861, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuation of racial discrimination\"\n}", "text": "i do this and the looks i get out of disgust are regular but i happily carry on walking and laugh them off , they think showing me a few pictures of negro kids on a poster without a burger is going to make me want to give money to them ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.", "processing_time": 0.987926721572876, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.\"\n}", "text": "Hello Everyone on Stormfront Have heard a lot of good things about this forum so I have decided to take the plunge .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement is a comparison and does not contain offensive content.", "processing_time": 0.8561370372772217, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement is a comparison and does not contain offensive content.\"\n}", "text": "Lions , Tigers , Cheetahs , etc are all cats but they ai n't the same .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting a specific individual based on their ethnicity and cultural background.", "processing_time": 1.0361075401306152, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting a specific individual based on their ethnicity and cultural background.\"\n}", "text": "The chairman of the FA is one <PERSON> and , yes , he is a member of a certain tribe that delight in bringing diversity and the benefits of multi-culturalism and ' anti-racism ' , along with boatloads of third-worlders to Britain .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech; it simply expresses a personal opinion.", "processing_time": 0.9630553722381592, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech; it simply expresses a personal opinion.\"\n}", "text": "I'm in Australia and I want it to be done and over with , my mum is constantly going on about the Olympics like it 's a big deal .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it's just asking about Japanese Pancrase tournaments.", "processing_time": 1.053274154663086, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it's just asking about Japanese Pancrase tournaments.\"\n}", "text": "Yes, have you seen the Pancrase tornouments in Japan ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9906253814697266, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "We do not want `` assimilation '' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term suggesting bias against free speech", "processing_time": 0.8070473670959473, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term suggesting bias against free speech\"\n}", "text": "Dare to speak out in a country with supposed free speech and you 're labelled a bigot !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial exclusion and derogatory tone towards ethnic minorities", "processing_time": 0.851485013961792, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial exclusion and derogatory tone towards ethnic minorities\"\n}", "text": "You Anglo-Americans are all welcome to England if you can perform the ' small ' task of riding London of it 's ethnics !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "A neutral expression of gratitude without offensive content", "processing_time": 0.6650388240814209, "raw_model_output": "{\"verdict\": 0, \"explanation\": \"A neutral expression of gratitude without offensive content\"}", "text": "Thank you brother .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement expresses an opinion on perceived dishonesty without targeting specific groups or using offensive language.", "processing_time": 1.0288097858428955, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement expresses an opinion on perceived dishonesty without targeting specific groups or using offensive language.\"\n}", "text": "I was sure that they were lying about the numbers and it 's must be the same in every country IMO .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses an opinion about the cleanliness of cities but does not contain offensive speech.", "processing_time": 0.9841773509979248, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses an opinion about the cleanliness of cities but does not contain offensive speech.\"\n}", "text": "Of course the county is lovely , in facts its gods country in our book BUT the cities are rat infested with enrichers , just how the country is isnt it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain offensive language or express hatred towards any group.", "processing_time": 0.9020183086395264, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain offensive language or express hatred towards any group.\"\n}", "text": "I 'll post as much as I can , but I 'm more of a BNP supporter than an expert .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit criticism and encouragement of violence against a specific group, indicating hatred and threat.", "processing_time": 0.9828705787658691, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit criticism and encouragement of violence against a specific group, indicating hatred and threat.\"\n}", "text": "Here's an example of red violence against patriotic St George 's Day marchers and a red site encouraging even more of it the next year . Stop the March for England 2013 ( from Rough Music ) | Brighton Anti-fascists", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting language towards immigrants and their cultural contributions", "processing_time": 0.88718581199646, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting language towards immigrants and their cultural contributions\"\n}", "text": "Thanks to the imported filth that , like our land under its current occupation .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any offensive content; it simply describes an action and context.", "processing_time": 0.9744734764099121, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any offensive content; it simply describes an action and context.\"\n}", "text": "I was trying to find out if the Egyptians were white or not and my search brought me to a thread on this site .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.026289701461792, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Its got to be a quote from someone .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement describes an observed behavior but does not contain explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.1213560104370117, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement describes an observed behavior but does not contain explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "There have been times when I have walked past some nigerian or skinny and they will cough with hand at their side without care for those around them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racist and genocidal content targeting immigrants and implying violent overthrow of government.", "processing_time": 0.9930346012115479, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racist and genocidal content targeting immigrants and implying violent overthrow of government.\"\n}", "text": "Its about time we as a nation take matters into our own hands , over throw the government stop the white genocide deport all immigrants and establish a government to help indigenous white Britons rebuild this country .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about prison conditions without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1531574726104736, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about prison conditions without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Any bother u go on lock down no one gets out the cells so its not that bad prison is 24 hour boredom ..", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses difficulty in scheduling social activities without containing offensive content.", "processing_time": 0.8979754447937012, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses difficulty in scheduling social activities without containing offensive content.\"\n}", "text": "I've tried to meet for coffee with a few people but everybody is really spread out , it 's tough to find the time to go out and meet like minded people .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0180535316467285, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "If you come down n ' around Sacramento California area send me a message .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The query seeks clarification on terms rather than expressing offensive content.", "processing_time": 0.8735659122467041, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The query seeks clarification on terms rather than expressing offensive content.\"\n}", "text": "Hi i need some information to understand the meaning of the negrogreek homo and the race-mixer tag of the HELLENIC THREAD .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement criticizes actions but does not contain explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.1014997959136963, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement criticizes actions but does not contain explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "<PERSON> <PERSON> is a German patriot and musician who , in the sick minds of ZOG , committed the heinious crime of producing music for a local school .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content directed towards a specific religious group", "processing_time": 0.9109146595001221, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content directed towards a specific religious group\"\n}", "text": "Hi everyone .... I 'm from Slovenia and I utterly hate every living muslim in this world ....... and I despise even the dead ones", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9946589469909668, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "If you are not registered I think that maybe you will not be able to see this .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9773826599121094, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Lets keep it that way and not destroy it Check out this ice cave in Dobšiná Its frigging cold in there", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language towards a specific group (Russian Federation).", "processing_time": 0.920072078704834, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language towards a specific group (Russian Federation).\"\n}", "text": "If I was Ukrainian , I 'd sign a pact with devil himself to get rid of jewdo-islam Putinistan AKA Russian Federation ....", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'libtard' targeting a political group", "processing_time": 0.8878836631774902, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'libtard' targeting a political group\"\n}", "text": "Too many libtards , I would n't waste my time there .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial stereotypes and insinuates blame towards a specific group", "processing_time": 0.8677494525909424, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial stereotypes and insinuates blame towards a specific group\"\n}", "text": "Blacks in South Africa wondering why their country is going to hell when they put Blacks in charge .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to express frustration rather than containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.070662498474121, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to express frustration rather than containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Please stop this nonsense all of you !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implication towards a continent, suggesting violence and negative behavior universally", "processing_time": 0.9366381168365479, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implication towards a continent, suggesting violence and negative behavior universally\"\n}", "text": "They are beating each other to death everywhere , just look what they do to each other in Africa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text expresses a preference for a specific race, which can be considered discriminatory.", "processing_time": 0.9566407203674316, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text expresses a preference for a specific race, which can be considered discriminatory.\"\n}", "text": "Hey, I 'm looking to meet new people maybe find myself a nice white boy .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive statement about a personal pet", "processing_time": 0.7693586349487305, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive statement about a personal pet\"\n}", "text": "saysay.jpg say say with a candle 2.jpg This is my nearly 3 year old cat called <PERSON><PERSON><PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language targeting a specific religious group", "processing_time": 0.83469557762146, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language targeting a specific religious group\"\n}", "text": "I got sloppy seconds after some Muslims used this girl .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9652743339538574, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "The ability to run really quietly and lots of chloroform .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.8600561618804932, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "haha; im a 19 y.o white male from newcastle area - but im moving back to sydney very soon ( west sydney im afraid lol )", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text appears to express well-wishes and does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1665420532226562, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text appears to express well-wishes and does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "To me and the rest of my commrades on this wonderfull White Day !!! I hope you have loads of fun and be safe !!!!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content towards specific groups.", "processing_time": 0.9112026691436768, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content towards specific groups.\"\n}", "text": "The only Mexican in town has since moved out and kids can now leave their bicycles in the yard without fear .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.978506326675415, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "If her eyes linger ................................... she 's yours .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is neutral and does not contain any offensive content.", "processing_time": 0.8254578113555908, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is neutral and does not contain any offensive content.\"\n}", "text": "It is always nice to meet another person with roots in Kansas .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses a personal desire for family composition without containing offensive content.", "processing_time": 0.8758080005645752, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses a personal desire for family composition without containing offensive content.\"\n}", "text": "I want to have 3 children ! 2 girls and 1 boy but the hardest part now is i got to try and find someone to make them with , but so far no luck", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9799706935882568, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Only the crazy ones stay .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text appears to describe a boat and its motor specifications without any offensive content.", "processing_time": 0.9339470863342285, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text appears to describe a boat and its motor specifications without any offensive content.\"\n}", "text": "A 750 lb boat with a 280 hp motor .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is neutral and does not contain any offensive content.", "processing_time": 0.8468687534332275, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is neutral and does not contain any offensive content.\"\n}", "text": "My buddy 's boat is anchored beside it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and gender-based insinuation suggesting superiority and implying potential violence", "processing_time": 0.9291293621063232, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and gender-based insinuation suggesting superiority and implying potential violence\"\n}", "text": "Of course , they are more virile than us , so that would n't happen would it ?!!!", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement appears to express personal preference without containing offensive content.", "processing_time": 0.839937686920166, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement appears to express personal preference without containing offensive content.\"\n}", "text": "I have a dominant personality and I look for the same thing in girls .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0116539001464844, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I can get a ride to see you some time this summer if that is cool i need to get up with someone in NJ as it is his name is <PERSON><PERSON> i have talk to him but i want to meet him so when i go to see him I can stop to see you if that is ok", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9829497337341309, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "hey whats up i 'm new on here and i would like to have someone to talk to so hit me back for whatever", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit encouragement of hate and intolerance towards others", "processing_time": 0.7757542133331299, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit encouragement of hate and intolerance towards others\"\n}", "text": "Maybe spread some HATE and intolerance ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9661409854888916, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Everyone on my MSN gets all manner of pics ( ) but I 've never put any up here so here goes Some pics from about a year ago and a small anigif from a week ago heh Robin", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9927506446838379, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "hi read your post and my name is jam<PERSON> from australia , and would like to well write to such a pleasent person as yourself have a nice day !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a personal anecdote without any offensive content.", "processing_time": 0.8569557666778564, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a personal anecdote without any offensive content.\"\n}", "text": "I used to enter horse competitions until I was thrown off one and broke my leg .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is neutral and does not contain any offensive content.", "processing_time": 0.8380532264709473, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is neutral and does not contain any offensive content.\"\n}", "text": "There have been more people coming on this board that live in the central Wisconsin area since I have started coming on here .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and expression of extreme political views", "processing_time": 0.8005595207214355, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and expression of extreme political views\"\n}", "text": "They 're music is one of the reasons I decided to become a white nationalist in the first place .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.8571586608886719, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "I 'll be at that show AND I 'm a brunette .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses concern about pricing issues among different groups but does not contain offensive language.", "processing_time": 0.9644303321838379, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses concern about pricing issues among different groups but does not contain offensive language.\"\n}", "text": "Someone Please Tell me what can be done or which people to tell that the Mexicans and or other hispanics here are undercutting price quotes on Good working People .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and exclusionary language targeting a specific group based on race.", "processing_time": 0.9342999458312988, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and exclusionary language targeting a specific group based on race.\"\n}", "text": "DuetschGirlTX, No I live in a tiny ass town where i am only one of 100 white folks ( maybe less ) . near San Antonio. there are so many dirts around and they all stick together. so when it comes to a White Girl in this town i gotta have my own back ALWAYS Is that where you are ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text does not contain any offensive content.", "processing_time": 0.7693710327148438, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text does not contain any offensive content.\"\n}", "text": "<PERSON>, I am 26yo from Glendale AZ .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text describes job responsibilities without any offensive content.", "processing_time": 0.8129405975341797, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text describes job responsibilities without any offensive content.\"\n}", "text": "The work involves : selling sporting goods and assisting customers with questions or problems .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9950351715087891, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I do n't go line dancing but I listen to country and want to live on a ranch or farm", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and gender-specific language, potentially implying discrimination.", "processing_time": 0.8484315872192383, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and gender-specific language, potentially implying discrimination.\"\n}", "text": "KC skin boy looking for KC skin girl .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and hatred directed towards a specific group.", "processing_time": 0.8353066444396973, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and hatred directed towards a specific group.\"\n}", "text": "what about it. that 's exactly what I said and I meant every word he thought I might and that was enough and with you two talking behind my back like some jew what do you expect me to say have a nice life .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0151152610778809, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I do n't think so .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0294241905212402, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I lived in texas when i was younger , i know how tough some things can get . `` rasslin animals '' is half the fun", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain offensive content; it's a neutral instruction.", "processing_time": 0.9053990840911865, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain offensive content; it's a neutral instruction.\"\n}", "text": "Just Google it and you 'll see what I mean .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.9203884601593018, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "I have decided to take this debate to pm .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text discusses a political issue without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0667059421539307, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text discusses a political issue without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Living in a boarder state , Texas , the problems of illegal aliens is , naturally , a stark reality , and let me add that the only solution that I see is the implimentation of U.S. Troops as guards on the Mexican boarder .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit promotion of hate and insinuation of continued negative sentiments towards unspecified groups.", "processing_time": 1.0082273483276367, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit promotion of hate and insinuation of continued negative sentiments towards unspecified groups.\"\n}", "text": "Although many have asked us to put it aside in politics , this thread is for all those who see where <PERSON><PERSON><PERSON> FITS IN and why WE NEED THE HATE WAVE TO CONTINUE .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content, just a statement about personal activity.", "processing_time": 0.8188953399658203, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content, just a statement about personal activity.\"\n}", "text": "Hi i have not been on here in ages .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9955096244812012, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "No I do n't support Nordic Superiority , but I have no problem with Nordic people who want a pure Nordic race to exist .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.015824317932129, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "if they have the same help here in Quebec , i would like too say congratulation pitbull-<PERSON><PERSON><PERSON><PERSON> and i hope you have lot of new white resident in your city", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and discriminatory content", "processing_time": 0.6841294765472412, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and discriminatory content\"\n}", "text": "Why would they let blacks live ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive content; it appears to be a polite introduction with a request to share comments.", "processing_time": 1.1275262832641602, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive content; it appears to be a polite introduction with a request to share comments.\"\n}", "text": "88 all , this is my first post and i 've read this whole thread so i guess I have a few comment 's , let 's start , shall we ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content against specific ethnic groups", "processing_time": 0.8537476062774658, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content against specific ethnic groups\"\n}", "text": "Black and Asian immigrants who are coming in and those who were borne here and their breading patterns are the threat to our gene pool and not other white Europeans such as polish people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content detected; statement appears neutral.", "processing_time": 0.7890398502349854, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content detected; statement appears neutral.\"\n}", "text": "This is my next read .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains implicit racial bias and suggests harmful actions towards 'lesser races'", "processing_time": 0.906578779220581, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains implicit racial bias and suggests harmful actions towards 'lesser races'\"\n}", "text": "Sadly, I think the government would step in if we tried to stop the lesser races from moving into our land .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content detected; statement appears neutral.", "processing_time": 0.7707507610321045, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content detected; statement appears neutral.\"\n}", "text": "This is my next read .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.8754076957702637, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "Absolutely.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Questioning historical facts without context or intent to offend", "processing_time": 0.8418657779693604, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Questioning historical facts without context or intent to offend\"\n}", "text": "Was n't <PERSON> the first `` black '' president ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Thank you statement, no offensive content present", "processing_time": 0.6656322479248047, "raw_model_output": "{\"verdict\": 0, \"explanation\": \"Thank you statement, no offensive content present\"}", "text": "TY!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred, or derogatory content found.", "processing_time": 0.9022195339202881, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred, or derogatory content found.\"\n}", "text": "BTW .. update coming soon with info on my drive back to Florida .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial insinuations and calls for exclusion/harm towards non-white individuals", "processing_time": 0.9912557601928711, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial insinuations and calls for exclusion/harm towards non-white individuals\"\n}", "text": "When the non-whites come we should stand together and send em packing , like they do to us .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting a specific individual based on race and physical appearance.", "processing_time": 0.9775505065917969, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting a specific individual based on race and physical appearance.\"\n}", "text": "I got stuck next to a really huge black woman who stunk to high heaven , and spent the entire ride with my face as close to the window as I could get .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial and gender-based discrimination and promotes division", "processing_time": 0.8023626804351807, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial and gender-based discrimination and promotes division\"\n}", "text": "Teach our men to be men again and defend our countries and women folk against the non white .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content, implying negative stereotypes about a racial group.", "processing_time": 0.9241564273834229, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content, implying negative stereotypes about a racial group.\"\n}", "text": "Most white girls are taught and brainwashed to love black guys , only way is to properly educate them and show them what savages they truly are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial stereotypes and derogatory language", "processing_time": 0.747061014175415, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial stereotypes and derogatory language\"\n}", "text": "White town saved by the magic negroes of Afreakkaland .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory comparison and stereotypes about specific groups", "processing_time": 0.8256759643554688, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory comparison and stereotypes about specific groups\"\n}", "text": "Actually Monkeys and Apes are more noble creatures and rarely act violent it really is unfare to the Monkeys and Apes to be compared with subhuman Negroids who are by contrast very murderous .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses a hope for a specific outcome regarding a group of people, without using explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.3279826641082764, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses a hope for a specific outcome regarding a group of people, without using explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "Hopefully they will leave Y<PERSON> alone and just deal with the Nigerians when they start chimping out in the streets .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting a specific group", "processing_time": 0.8453691005706787, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting a specific group\"\n}", "text": "I just refer to them as primates as through THEIR evolution process they are just a little bit higher on the evolutionary chain which makes them all the more dangerous to whites , than any of their animalistic cousins .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text appears to express concern about dealing with aggressive behavior without explicitly containing offensive language or targeting specific groups.", "processing_time": 1.1316823959350586, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text appears to express concern about dealing with aggressive behavior without explicitly containing offensive language or targeting specific groups.\"\n}", "text": "Amen brother ..... you did say it like you been there ... they talk crap and if you show fear they 'll be on ya fast .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text appears to express encouragement and solidarity without explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1024739742279053, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text appears to express encouragement and solidarity without explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "But get the eyes wide open and lets rock attitude ... most will back off and go away ..... been there with ya !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text lists individuals convicted of transmitting HIV/AIDS without expressing offensive speech.", "processing_time": 1.0272533893585205, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text lists individuals convicted of transmitting HIV/AIDS without expressing offensive speech.\"\n}", "text": "I'm not sure what sort of information you 're seeking but I recently found a number of Blacks who have been convicted for knowingly transmitting HIV/AIDS : <PERSON><PERSON><PERSON> http://news.bbc.co.uk/2/hi/uk_news/e...de/3389735.stmJohnsonAzigahttp://en.wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-Yondohttp://en.wikipedia.org/wiki/Stan<PERSON><PERSON>_<PERSON>-YondjoTrevisSmithhttp://en.wikipedia.org/wiki/Trevis_SmithAdrienSylverNduwayohttp://www.thenownews.com/issues06/0...031206nn2.htmlWillieAtkinshttp://www.dallasnews.com/sharedcont....32a4d9fd.htmlNushawnWilliamshttp://en.wikipedia.org/wiki/Nushawn_WilliamsSimonMolhttp://en.wikipedia.org/wiki/<PERSON>_<PERSON>onBandahttp://www.dailymail.co.uk/pages/liv...=5&expand=truePeterMwaihttp://www.crime.co.nz/c-files.asp?ID=36WilliamKaranjahttp://www.amren.com/mtnews/archives...n_rapist_j.phpLinkoyMuhurihttp://news.bbc.co.uk/2/hi/uk_news/e...on/3256352.stmMohammedDicahttp://news.bbc.co.uk/2/hi/uk_news/e...on/3190626.stmFestonKonzanihttp://news.bbc.co.uk/2/hi/uk_news/e...es/4267433.stmGabrielVengesaihttp://www.dailymail.co.uk/pages/liv ... n _ page _ id = 1770", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text discusses consequences under certain policies without expressing offensive speech.", "processing_time": 0.8533318042755127, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text discusses consequences under certain policies without expressing offensive speech.\"\n}", "text": "You do not have to wait for this law to pass since already you would be expelled from your school for doing the above under some sort of hostile school environment clause , as you would at most places of work .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains implicit racial bias and insinuations of criminal activity based on race", "processing_time": 0.9399549961090088, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains implicit racial bias and insinuations of criminal activity based on race\"\n}", "text": "http://www.abc25.com/Global/story.asp ? S = 5352185 Non whites commit far more crimes against whites each year but its never considered a hate crime and alot of them never spend any time in jail !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain offensive content; it's discussing historical perspectives.", "processing_time": 0.8864037990570068, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain offensive content; it's discussing historical perspectives.\"\n}", "text": "And some people say that the Klan is n't doing anything today to make a difference .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content regarding interracial relationships", "processing_time": 0.8071413040161133, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content regarding interracial relationships\"\n}", "text": "Maybe God was punishing them for their interracial marriage .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content targeting a specific racial group.", "processing_time": 0.8490862846374512, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content targeting a specific racial group.\"\n}", "text": "The Jewish race is without any doubt the greatest threat to the survival of the White race .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This sentence does not contain any offensive content; it simply describes following someone's adventures on a platform.", "processing_time": 1.098365068435669, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This sentence does not contain any offensive content; it simply describes following someone's adventures on a platform.\"\n}", "text": "I've followed Mr<PERSON> 's adventures through VNN for the last couple years .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insult and hatred towards a specific group", "processing_time": 0.7974767684936523, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insult and hatred towards a specific group\"\n}", "text": "I just wanted to say hello to The Jew is Evil .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful language targeting a specific group", "processing_time": 0.8536026477813721, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful language targeting a specific group\"\n}", "text": "The kikes probably put the Leftist up to it , to make it seem like they do n't have total control of everyone .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial slurs and violent threats against a specific group", "processing_time": 0.8718369007110596, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial slurs and violent threats against a specific group\"\n}", "text": "These feral negroes stalk white women , rape and kill and , no one seems willing to stop them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to describe a mindset rather than containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1741549968719482, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to describe a mindset rather than containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "This is where their `` I 'm a bad ass , I can do whatever I want '' attitute comes from .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to offer advice rather than being insulting, threatening, or hateful.", "processing_time": 0.9813029766082764, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to offer advice rather than being insulting, threatening, or hateful.\"\n}", "text": "Indeed, now they just need to feed themselves , educate themselves , police themselves ad nauseum ......", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement makes generalizations about racial characteristics without expressing offense, hatred, or threats.", "processing_time": 1.005845069885254, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement makes generalizations about racial characteristics without expressing offense, hatred, or threats.\"\n}", "text": "Both Asians and whites are composed and calm and blacks seem to have no self control .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and stereotypes based on nationality", "processing_time": 0.8027245998382568, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and stereotypes based on nationality\"\n}", "text": "About the Chinese , those who remain in their own country are good , those who become illegal immigrants in white nations are bad .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and xenophobic content, advocating for exclusion based on nationality.", "processing_time": 0.9016714096069336, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and xenophobic content, advocating for exclusion based on nationality.\"\n}", "text": "Why not just kick out all those Africans ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting specific ethnic groups", "processing_time": 0.8465521335601807, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting specific ethnic groups\"\n}", "text": "Those in the countries you mentioned are the mongrel offspring between whites ( Aryans ) and Arabs and its funny how those of the East who claim to be the Aryans talk about white people who came and mixed with them and the only Aryans apparently in the East are those with light skin and blue eyes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains threatening and degrading language suggesting surrender and defeat", "processing_time": 0.831223726272583, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains threatening and degrading language suggesting surrender and defeat\"\n}", "text": "Exactly, they want us to lay down and die .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content directed towards a specific group", "processing_time": 0.8748772144317627, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content directed towards a specific group\"\n}", "text": "By their words and deeds , they declare themselves to be staunchly in favor of the genocide of our people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and derogatory content targeting specific racial and ethnic groups.", "processing_time": 0.9287924766540527, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and derogatory content targeting specific racial and ethnic groups.\"\n}", "text": "Then we can all live in a global utopia with the Jew running the show - cant wait Nothing short of a brain transplant is going to raise a Negroes IQ .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The phrase 'pile of burned offerings' does not inherently convey explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.2008731365203857, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The phrase 'pile of burned offerings' does not inherently convey explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "A pile of burned offerings .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial hatred and derogatory terms", "processing_time": 0.7956790924072266, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial hatred and derogatory terms\"\n}", "text": "oddly i would find it worst if she here black , since Negroes are the most inferior and scum bag humanoid race in our planet .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term directed at specific group", "processing_time": 0.7823984622955322, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term directed at specific group\"\n}", "text": "Good luck with your bugchasing , homo . . .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and threats directed towards a specific group", "processing_time": 0.8376674652099609, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and threats directed towards a specific group\"\n}", "text": "Laugh it up ape cause without yt to protect your race Asians will probably do the world a favour and wipe your kind from the face of the Earth .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful content towards a specific group, advocating against their rights and implying genocide.", "processing_time": 1.068152666091919, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful content towards a specific group, advocating against their rights and implying genocide.\"\n}", "text": "Gay marriage has no place in any society , regardless of your race and culture.It destroys , morals , values , and the future of your offspring that will keep your race and culture alive.Regardless of your race and culture , homosexuality and gay marriage is the genocide of mankind .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful content targeting a specific group", "processing_time": 0.8483321666717529, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful content targeting a specific group\"\n}", "text": "In all likelyhood , it 's jews painting the road of skank for her to follow .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement criticizes the act of making truth investigation illegal without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.191255807876587, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement criticizes the act of making truth investigation illegal without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "But millions can believe a lie for centuries upon centuries , especially when you make investigating the story a crime .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and derogatory language targeting a specific group.", "processing_time": 0.8755459785461426, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and derogatory language targeting a specific group.\"\n}", "text": "That type of negroes call your kind of brothas , Uncle <PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist implications towards sub-Saharan Africans", "processing_time": 0.8189465999603271, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist implications towards sub-Saharan Africans\"\n}", "text": "We knew that these structures were built by sub-saharan blacks in the same way we knew that the civilization Greek and Romanian built by white Europeans .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting a specific ethnic group", "processing_time": 0.8396458625793457, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting a specific ethnic group\"\n}", "text": "The only good thing about Indians is their married women are so loose that prostitution fails in Indian areas .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content directed at a specific group", "processing_time": 0.7840368747711182, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content directed at a specific group\"\n}", "text": "Stop showing off South Asian functional illiteracy .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text provides demographic data for Miami without expressing offensive speech.", "processing_time": 0.9352495670318604, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text provides demographic data for Miami without expressing offensive speech.\"\n}", "text": "Miami,( I do n't know how <PERSON> can stand living down there ) Hispanic - 279,456 ( 70.0 % ) Black alone - 64,993 ( 16.3 % ) White alone - 47,622 ( 11.9 % ) Asian alone - 3,649 ( 0.9 % ) Two or more races - 2,642 ( 0.7 % ) Other race alone - 676 ( 0.2 % ) American Indian alone - 361 ( 0.09 % ) Native Hawaiian and Other Pacific Islander alone - 58 ( 0.01 % ) Read more : Miami , Florida ( FL ) profile : population , maps , real estate , averages , homes , statistics , relocation , travel , jobs , hospitals , schools , crime , moving , houses , news , sex offenders", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults directed at a specific group", "processing_time": 0.7954807281494141, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults directed at a specific group\"\n}", "text": "The majority of them are as stupid as real Negroes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting racial groups", "processing_time": 0.801687479019165, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting racial groups\"\n}", "text": "I do not think anyone looks up to white Negroes , just the black ones and then they become wiggers .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0551848411560059, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "We just do n't like what we see .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement describes an experience without containing offensive content.", "processing_time": 0.****************, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement describes an experience without containing offensive content.\"\n}", "text": "I tried to sign up there once & they refused to activate my account .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and derogatory language targeting a specific group.", "processing_time": 0.****************, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and derogatory language targeting a specific group.\"\n}", "text": "Negroids could n't put together a bake sale properly much less a work force .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content implying inferiority", "processing_time": 0.****************, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content implying inferiority\"\n}", "text": "The Black race is yet to invent any kind of civilization .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language towards a specific race", "processing_time": 0.833275318145752, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language towards a specific race\"\n}", "text": "What does any of that have to do with racial preservation and the resistance to the deliberate and calculated extermination of the White race and the destruction of western civilization which 99 % of the members of this site are concerned with ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement appears neutral, expressing an observation without explicit offense.", "processing_time": 0.8472151756286621, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement appears neutral, expressing an observation without explicit offense.\"\n}", "text": "They almost always choose to identify with blacks more than whites .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and discriminatory content", "processing_time": 0.6810958385467529, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and discriminatory content\"\n}", "text": "The White race is best because when the <PERSON><PERSON> made Whites , He made them better than He made the other races .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting remarks targeting a specific group based on physical characteristics.", "processing_time": 0.9207749366760254, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting remarks targeting a specific group based on physical characteristics.\"\n}", "text": "Africans are tall , thin and lanky and pretty ugly beasts . ... and I 'm just being kind .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting a specific racial group", "processing_time": 0.8588905334472656, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting a specific racial group\"\n}", "text": "To me it means that Jewish supremacists will never again allow Whites to have a say .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain offensive content; it appears to express an opinion on the consequences of non-support.", "processing_time": 1.076857566833496, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain offensive content; it appears to express an opinion on the consequences of non-support.\"\n}", "text": "The above poster is correct when we stop supporting then they 're in terrible danger .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and implies racial hatred towards Jews and liberals", "processing_time": 0.894155740737915, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and implies racial hatred towards Jews and liberals\"\n}", "text": "In the eyes of the Jew and the libtard there is a race problem and the problem is us !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting specific groups such as university students, athletes, and women.", "processing_time": 1.0538678169250488, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting specific groups such as university students, athletes, and women.\"\n}", "text": "I cannot imagine how revolting it must be - the PC dialogue at your university -- idolizing athletes who dont belong in college -- white women getting pregnant with freaks", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful content directed towards a specific group", "processing_time": 0.8898241519927979, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful content directed towards a specific group\"\n}", "text": "Thank you for posting this ! this disgusting lie will hopefully one day be addressed and corrected , at that point I would hope the world will see the Jews for what they really are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement expresses an opinion on immigration policies without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1419169902801514, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement expresses an opinion on immigration policies without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "As a nation , we are in desperate need for an immigration time out and a zero tolerance policy for those here illegally .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms targeting specific groups", "processing_time": 0.7518801689147949, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms targeting specific groups\"\n}", "text": "I am not going to be beat down by a dyke , fag , or coon .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it's just offering assistance.", "processing_time": 0.9412195682525635, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it's just offering assistance.\"\n}", "text": "I can give you her <PERSON><PERSON> and she will answer all your questions .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement expresses an opinion without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.031857967376709, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement expresses an opinion without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Nearly everything if not all acts that the liberals carry out damage this nation and our people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and explicit content targeting a specific group", "processing_time": 0.8290073871612549, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and explicit content targeting a specific group\"\n}", "text": "Ya got a virgin shooter .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0024933815002441, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Ten to one they have a great father , too .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content, statement seems supportive and positive.", "processing_time": 0.7719483375549316, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content, statement seems supportive and positive.\"\n}", "text": "It 's where I discovered so many great people who are fighting for our cause .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This sentence does not contain any offensive content; it simply describes an action.", "processing_time": 0.9367618560791016, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This sentence does not contain any offensive content; it simply describes an action.\"\n}", "text": "I 've taught her to question everything that they teach her .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is about selling classical music CDs with no offensive content.", "processing_time": 0.9056048393249512, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is about selling classical music CDs with no offensive content.\"\n}", "text": "Here are the CDs I want to sell : <PERSON> : Symphonies Nos. 5 & 7 - <PERSON><PERSON><PERSON> : Symphony No. 9 - <PERSON><PERSON><PERSON><PERSON> Bruckner : Symphony No. 9 - <PERSON><PERSON><PERSON><PERSON> : Music for the Royal Fireworks ; Water Music - <PERSON><PERSON><PERSON> <PERSON> : Orchestral Works - Schermerhorn J.S. Bach : Great Organ Works - Hurford Norwegian Classical Favorites - Engeset The Best of Baroque Music - <PERSON><PERSON> Twilight of the Gods : The Essential Wagner Collection - <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON> Most of these were recommended by fellow Stormfronters .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "A neutral statement expressing gratitude, no offensive content present.", "processing_time": 0.8171606063842773, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"A neutral statement expressing gratitude, no offensive content present.\"\n}", "text": "Thanks for coming", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and threat directed towards a group", "processing_time": 0.8374810218811035, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and threat directed towards a group\"\n}", "text": "The people running SA into the ground , want to wipe them all out .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears to express an opinion on perceived differences without explicitly containing offensive content.", "processing_time": 0.9732940196990967, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears to express an opinion on perceived differences without explicitly containing offensive content.\"\n}", "text": "Sometimes I wonder if it is Because Whites are so much smarter that they find it harder to run together , as opposed to , independently .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion on a video's claim about diversity affecting social cohesion without using offensive language.", "processing_time": 1.0738556385040283, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion on a video's claim about diversity affecting social cohesion without using offensive language.\"\n}", "text": "Check out this youtube channel i found , i agree with most of what he says Diversity DESTROYS Social Cohesion in the West - YouTube", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text provides information about a presentation on false confessions by <PERSON>.", "processing_time": 0.9787254333496094, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text provides information about a presentation on false confessions by <PERSON>.\"\n}", "text": "<PERSON> : `` False confessions '' https : //www.youtube.com/watch ? v = JDRRwFfJKkw The Vera Institute of Justice 's <PERSON> Research Department Speaker Series presents <PERSON> , Distinguished Professor of Psychology at John Jay College of Criminal Justice .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9935855865478516, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "It 's just the way they are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement implies political bias and suggests illegal activity by a government agency against a specific political group, which can be considered as containing derogatory and potentially threatening content.", "processing_time": 1.4016313552856445, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement implies political bias and suggests illegal activity by a government agency against a specific political group, which can be considered as containing derogatory and potentially threatening content.\"\n}", "text": "You need to ask this when the Obama Administration instructed the IRS to target conservatives ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0111558437347412, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I 'm hoping things will continue to grow with our smaller meets and will make for a good turn out at our events this year !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language suggesting ethnic cleansing, which implies hatred and threat towards a group", "processing_time": 0.9694559574127197, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language suggesting ethnic cleansing, which implies hatred and threat towards a group\"\n}", "text": "I considered it being `` ethnically cleansed '' from my childhood home", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is an opinion about the intelligence of the <PERSON> brothers and does not contain offensive language.", "processing_time": 1.0027287006378174, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is an opinion about the intelligence of the <PERSON> brothers and does not contain offensive language.\"\n}", "text": "Neither of the <PERSON> brothers are very bright but they generally come to fight .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is about personal living conditions and does not contain any offensive content.", "processing_time": 0.9228496551513672, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is about personal living conditions and does not contain any offensive content.\"\n}", "text": "I live in a suburban area with an old spring fed lake that always has water in it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a neutral expression of academic performance.", "processing_time": 0.7692661285400391, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a neutral expression of academic performance.\"\n}", "text": "Nothing happened except I got an 88 % on the paper .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and expressions of hatred towards Jewish people", "processing_time": 0.8437354564666748, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and expressions of hatred towards Jewish people\"\n}", "text": "Well, at least these dumb negroes got the fact that Jews are evil right ; though I suspect whitey and the jews are one in the same to their primitive minds ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and hatred directed towards white people", "processing_time": 0.7684876918792725, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and hatred directed towards white people\"\n}", "text": "I am sure California is full of sick self hating whites , who think that for the good of all the black brown and yellow peoples of the world , whites need to commit racial suicide and disappear from their face of the earth for their sake and wo n't that be just dandy !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting non-white individuals", "processing_time": 0.8051397800445557, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting non-white individuals\"\n}", "text": "I do n't blame you , all the yucky nonwhite DNA pawing your food as they pass it over the UPC scanner , disgusting .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and stereotypes directed towards specific ethnic groups", "processing_time": 0.8318638801574707, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and stereotypes directed towards specific ethnic groups\"\n}", "text": "All of our tim hortons are staffed by those nast filipinos and every single town is full of Indians and asians .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting racial mixing and implying negative consequences for a race", "processing_time": 0.9535586833953857, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting racial mixing and implying negative consequences for a race\"\n}", "text": "And theres a good chance these children will end up race mixing back into the white gene pool , mongrelizing our race .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content, implying a negative view towards a racial group.", "processing_time": 0.9428534507751465, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content, implying a negative view towards a racial group.\"\n}", "text": "Then again , in this modern age of white youth emulating negroes and white adults groveling at the feet of the juden , perhaps it 's an appropriate word after all .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial discrimination, hate speech, and calls for deportation and sterilization", "processing_time": 0.9370744228363037, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial discrimination, hate speech, and calls for deportation and sterilization\"\n}", "text": "We need to deport all nonwhites immediately , that or stop letting more in and sterilize the ones here , that will fix the problem .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implications and racial bias", "processing_time": 0.735734224319458, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implications and racial bias\"\n}", "text": "If Canada was small the nonwhites would be everywhere but so many whites live rurally or in small towns so they do n't see the racial disaster unfolding on Canada .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is neutral and does not contain any offensive content.", "processing_time": 0.8261244297027588, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is neutral and does not contain any offensive content.\"\n}", "text": "I've got two knife blocks on the counter .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text describes a scenario without any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.032503604888916, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text describes a scenario without any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "At the resturant , the Pres. of Hamms orders a Hamms , the Pres. of Schlitz orders a Schlitz , the Pres. of Budweiser orders a Bud , and the Pres. of <PERSON><PERSON><PERSON> orders a cup of tea .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain any offensive content.", "processing_time": 0.7501802444458008, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain any offensive content.\"\n}", "text": "The school did not cause my much trouble because it was elementary school .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement criticizes the news without explicitly targeting a specific group or using derogatory language.", "processing_time": 0.9863598346710205, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement criticizes the news without explicitly targeting a specific group or using derogatory language.\"\n}", "text": "We say here that if we want to be lied to just watch the news !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slur and violent context", "processing_time": 0.704641580581665, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slur and violent context\"\n}", "text": "I went outside and the ni<PERSON>t 's mom told me my daughter had hit her first .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement presents historical facts without any offensive content.", "processing_time": 0.7737116813659668, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement presents historical facts without any offensive content.\"\n}", "text": "The Neolithic farmers who migrated into Europe originated in Anatolia .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language targeting a political group.", "processing_time": 0.7925117015838623, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language targeting a political group.\"\n}", "text": "Explain how liberals are liars and anti-White and tell her about crime statistics .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.921332836151123, "raw_model_output": "{\"verdict\": 0, \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"}", "text": "Maybe you 'll find it useful .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9638328552246094, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Chicago Teachers Union president explains how to inject politics into math - YouTube Chicago Teachers Union president <PERSON> explains how to politicize school math for organizing - EAGnews.org powered by Education Action Group Foundation , Inc.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and threatening language towards a specific location and its inhabitants", "processing_time": 0.8941333293914795, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and threatening language towards a specific location and its inhabitants\"\n}", "text": "Maybe you should look there or just move here to miss. Or more specifically Tupel<PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content towards racial groups", "processing_time": 0.7714076042175293, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content towards racial groups\"\n}", "text": "All we have is blacks and a few mestizos and all are pretty well behaved , we keep them that way around here", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text provides personal background information without any offensive content.", "processing_time": 0.8104279041290283, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text provides personal background information without any offensive content.\"\n}", "text": "My father was born in Germany My mother 's parent 's moved from Ireland .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content directed at a group", "processing_time": 0.7402772903442383, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content directed at a group\"\n}", "text": "Lying Marxists !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and derogatory content directed towards a specific group based on nationality and ethnicity.", "processing_time": 0.9813904762268066, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and derogatory content directed towards a specific group based on nationality and ethnicity.\"\n}", "text": "Lay her off and cart her back to India where she can poop in the Ganges river next to her dead grand parents .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This sentence does not contain any offensive content; it simply states a fact.", "processing_time": 0.9145286083221436, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This sentence does not contain any offensive content; it simply states a fact.\"\n}", "text": "It offers certificates of completion for many courses .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and insinuation of disrespect towards someone's grammar skills", "processing_time": 0.9240479469299316, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and insinuation of disrespect towards someone's grammar skills\"\n}", "text": "Perhaps people will take you more seriously if you do n't make as many grammar errors and using repulsive language .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting a group", "processing_time": 0.7882306575775146, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting a group\"\n}", "text": "The only thing that they care about is welfare and mooching of all the rest of us .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses dislike for a genre without targeting specific groups or individuals", "processing_time": 0.8482723236083984, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses dislike for a genre without targeting specific groups or individuals\"\n}", "text": "I really do hate rap music .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses discomfort with loud music but does not contain offensive speech.", "processing_time": 0.897355318069458, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses discomfort with loud music but does not contain offensive speech.\"\n}", "text": "More than that I cringe every time I go to hang out with my friends and they blast this filth in their cars and homes .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears neutral, discussing a scientific topic without offensive content.", "processing_time": 0.8487045764923096, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears neutral, discussing a scientific topic without offensive content.\"\n}", "text": "And hey , we have not talked about women 's mitochondriat at all ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and implies racist conspiracy", "processing_time": 0.7560007572174072, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and implies racist conspiracy\"\n}", "text": "When they sent n iggers to space the space ships started blowing up .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement appears neutral, discussing the functionality of modern machinery.", "processing_time": 0.8425703048706055, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement appears neutral, discussing the functionality of modern machinery.\"\n}", "text": "Everything is machined to well today mlst stuff is plug and play .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text seeks community among gardeners and plant enthusiasts without expressing any offensive content.", "processing_time": 0.9347636699676514, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text seeks community among gardeners and plant enthusiasts without expressing any offensive content.\"\n}", "text": "Find other gardeners , wild plant enthusiasts , and fellow believers in your area who you can band with .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text describes meal preparation without containing any offensive content.", "processing_time": 0.815650224685669, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text describes meal preparation without containing any offensive content.\"\n}", "text": "We are cooking a duck on the rotisserie , catch the fat drippings and cook little potatoes in that , its delicious , we 're also cooking a small turkey , mashed potatoes , carrots , pumpkin pie and butternut squash cut in half and baked with butter and brown sugar .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content detected.", "processing_time": 0.5609674453735352, "raw_model_output": "{\"verdict\": 0, \"explanation\": \"No offensive content detected.\"}", "text": "Listening now .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text appears to be discussing cultural symbols without explicit offense.", "processing_time": 0.8524081707000732, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text appears to be discussing cultural symbols without explicit offense.\"\n}", "text": "Seriously Guys , It 's An Indian Symbol For Good Fortune - People Of Walmart : People Of Walmart", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and derogatory content directed towards racial and ethnic groups", "processing_time": 0.904127836227417, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and derogatory content directed towards racial and ethnic groups\"\n}", "text": "No need to allow FACTS to get in the way of a good White guilt message ! ... and the jew laughs loudly !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses an opinion about firearms without using explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.0990815162658691, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses an opinion about firearms without using explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "Or just stay the hell away from cheap , crap ar15s when you can get a damn reliable plinker for $ 700 brand new ......", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implications and promotes division based on sexual orientation", "processing_time": 0.8499319553375244, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implications and promotes division based on sexual orientation\"\n}", "text": "I'm glad you pointed this out that anti-Bullying campaign has been used to promote homosexuals who are more likely to bully anyone who disagrees with them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial and ethnic slurs, promoting hatred and discrimination", "processing_time": 0.8920621871948242, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial and ethnic slurs, promoting hatred and discrimination\"\n}", "text": "What makes it suck are the Jews that run it and the Blacks that play in it .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses personal preference regarding gameplay versus television viewing.", "processing_time": 0.8464741706848145, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses personal preference regarding gameplay versus television viewing.\"\n}", "text": "I enjoyed playing the game when I was a kid , but I never enjoyed watching it on TV .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0483720302581787, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Please let me know if there is anything I can do .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text appears to discuss ammunition specifications without containing offensive content.", "processing_time": 0.8786041736602783, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text appears to discuss ammunition specifications without containing offensive content.\"\n}", "text": "An even smaller pellet is probably ideal , #4 <PERSON> , perhaps .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it's a personal reflection.", "processing_time": 0.9168760776519775, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it's a personal reflection.\"\n}", "text": "I was just debating with myself whether I should go to bed or have one last beer ...", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains biased and potentially hateful generalization about race", "processing_time": 0.8052723407745361, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains biased and potentially hateful generalization about race\"\n}", "text": "Notice the video title , whenever there is a fight between a white and non white , the white is always the racist", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any offensive content.", "processing_time": 0.7744083404541016, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any offensive content.\"\n}", "text": "I see pro and cons of both .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement offers assistance rather than being insulting or threatening.", "processing_time": 0.8271458148956299, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement offers assistance rather than being insulting or threatening.\"\n}", "text": "Next time you see someone with their pants falling down , just help them !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful content directed towards a racial group", "processing_time": 0.8333408832550049, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful content directed towards a racial group\"\n}", "text": "White speech is Hate ....", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains implicit racial bias and exclusionary intent", "processing_time": 0.7807981967926025, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains implicit racial bias and exclusionary intent\"\n}", "text": "I have one word for you ....... MOVE . We chose this area because we wanted our children to go to all white schools .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain offensive speech; it's an opinion about parental political affiliations.", "processing_time": 0.9990980625152588, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain offensive speech; it's an opinion about parental political affiliations.\"\n}", "text": "Like I said before I 'd bet 90 % + have Liberal parents .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Questioning the existence of a border fence without context does not constitute offensive speech.", "processing_time": 0.9543919563293457, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Questioning the existence of a border fence without context does not constitute offensive speech.\"\n}", "text": "Border fence , what border fence ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text discusses observing troops using a firearm model without expressing any offensive content.", "processing_time": 0.9384257793426514, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text discusses observing troops using a firearm model without expressing any offensive content.\"\n}", "text": "I've been following the events in Ukraine and have noticed some troops using the SKS .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9975688457489014, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "He got what he deserved .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and derogatory language targeting a specific group.", "processing_time": 0.9119706153869629, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and derogatory language targeting a specific group.\"\n}", "text": "Back when I was a kid , even the black folks called them that .... `` Ooohhh I' z cai n't wait until <PERSON><PERSON><PERSON><PERSON> , I' z gonna get me sum of dem niggah-toes and oranguz fo ' <PERSON><PERSON>us<PERSON> , an ' sum peppahmintz ; dey shoa iz gud ! ''", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement merely describes a situation without expressing offense, hatred, or derogatory content.", "processing_time": 0.9965555667877197, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement merely describes a situation without expressing offense, hatred, or derogatory content.\"\n}", "text": "some times Whites are forced to live around a lot of blacks .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content detected; statement appears neutral.", "processing_time": 0.7636880874633789, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content detected; statement appears neutral.\"\n}", "text": "I live in AR .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slur and derogatory language towards teachers and a specific group", "processing_time": 0.9131424427032471, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slur and derogatory language towards teachers and a specific group\"\n}", "text": "I going to get homeschooled tired of looney lefty teachers and the negroes", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement describes a scenario without expressing offense, hatred, or derogatory content.", "processing_time": 0.947974681854248, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement describes a scenario without expressing offense, hatred, or derogatory content.\"\n}", "text": "I 've seen shots with a .45 , where people walked around angry .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial insults and promotes hatred towards a specific group", "processing_time": 0.8738045692443848, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial insults and promotes hatred towards a specific group\"\n}", "text": "I've often pointed out that negroes are more stupid than I can even imagine if they actually think they 're gonna win any race war .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuation of isolation towards a specific individual", "processing_time": 0.932318925857544, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuation of isolation towards a specific individual\"\n}", "text": "This guy seems to get it but otherwise : Sorry jigs , you 're on your own", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text describes a personal preference in fighting style and a record, without expressing hate, threats, or explicit insults.", "processing_time": 1.1318669319152832, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text describes a personal preference in fighting style and a record, without expressing hate, threats, or explicit insults.\"\n}", "text": "I'm more of a grappler than a striker , I would lunge out with a windpipe grab and bring them down to the floor .... my record for KO' ing someone with a choke is currently 3 secs", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting race and parenthood", "processing_time": 0.8648176193237305, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting race and parenthood\"\n}", "text": "Instead it is the athletic negro <PERSON><PERSON> 11 kids , <PERSON> 7 kids , <PERSON><PERSON><PERSON> 5 kids , <PERSON> 10 kids who procreate like apes and spread these athlete genes along .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content towards gender and race", "processing_time": 0.8192017078399658, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content towards gender and race\"\n}", "text": "Those boys will use the plastic whisks as swords , and the girls ' lego creations will consist of dollhouses for the few white dolls , with the dark ones in the adjoining farm pen .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement expresses an opinion without containing offensive content.", "processing_time": 0.8218703269958496, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement expresses an opinion without containing offensive content.\"\n}", "text": "The idea that homeschooled kids do n't get socialized is ludicrous .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults directed at specific groups", "processing_time": 0.7260153293609619, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults directed at specific groups\"\n}", "text": "2 .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory language towards a group", "processing_time": 0.8491814136505127, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory language towards a group\"\n}", "text": "When I watch things like this I pray God will have vengeance on these sub humans even if they have asked for forgiveness .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text appears to be a reference to a video interview with no indication of offensive speech.", "processing_time": 1.0581302642822266, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text appears to be a reference to a video interview with no indication of offensive speech.\"\n}", "text": "<PERSON><PERSON> and Dr. <PERSON> - Zion<PERSON> , the Holocaust , Aids and Vaccines YouTube - <PERSON><PERSON> and Dr. <PERSON> - Zionism , the Holocaust , Aids and Vaccines Listen/watch the full interview @ <PERSON><PERSON> and Dr. <PERSON> - Zion<PERSON> , the Holocaust , Aids and Vaccines : Free Download & Streaming : Internet Archive www.DrDay.com www.GoodNewsAboutGod.com", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text describes children in costumes without any offensive content.", "processing_time": 0.8247058391571045, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text describes children in costumes without any offensive content.\"\n}", "text": "Little kids all decked out in their costumes .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is expressing admiration for a documentary without any offensive content.", "processing_time": 0.8869607448577881, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is expressing admiration for a documentary without any offensive content.\"\n}", "text": "It 's a pretty powerful documentary that blew me away .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9637641906738281, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "But it does have a feminine sound .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0177569389343262, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Hi Im tv free do n't have kids yet reading mending , sewing , menting board games radio study extra curricula actives Good on you for planning a FREE jewv existence", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slur directed at a specific group", "processing_time": 0.7638325691223145, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slur directed at a specific group\"\n}", "text": "These ' niggas ' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a factual observation without any offensive content.", "processing_time": 0.8277609348297119, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a factual observation without any offensive content.\"\n}", "text": "There are only eight people living within a mile of my house , and none closer than a quarter-mile .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist content targeting a specific group", "processing_time": 0.8234567642211914, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist content targeting a specific group\"\n}", "text": "It's weird most Indians dot and to an extent feather that I deal with hate blacks more than they do whites and find blacks to be lazy and worthless .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit threat and derogatory content suggesting harm towards others", "processing_time": 0.8561151027679443, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit threat and derogatory content suggesting harm towards others\"\n}", "text": "Dude stink bomb this event , when it gets dark , chuck a bunch of them on the floor , different spots , when the chimps start jiving and grooving , they will step on them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content, just a statement about personal information.", "processing_time": 0.8248317241668701, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content, just a statement about personal information.\"\n}", "text": "Oh, I forgot to say where I lived .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting content directed towards specific groups", "processing_time": 0.8284597396850586, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting content directed towards specific groups\"\n}", "text": "They laugh at whites who obey the law , bother to get married , women who are chaste .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content towards a specific group", "processing_time": 0.8214535713195801, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content towards a specific group\"\n}", "text": "That 's to assume the gay ones are n't abusive .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and harmful content targeting sexual orientation and implying moral judgments.", "processing_time": 0.9234392642974854, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and harmful content targeting sexual orientation and implying moral judgments.\"\n}", "text": "the kind of perversion that leads to homosexuality also leads to pedophilia. why take the risk , and even if the homosexuals turned out not to be pedophiles why would anyone want a child to live with two perverts .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0162081718444824, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Hell to have a teacher like this .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuates moral justification for a specific group based on religious texts.", "processing_time": 1.054563045501709, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuates moral justification for a specific group based on religious texts.\"\n}", "text": "This is the argument homosexuals use to justify their behavior , that because there are no `` red letters '' about it , then homosexuality is not a bad thing .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9675939083099365, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "No .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement merely advocates for freedom of expression without containing any offensive content.", "processing_time": 0.9004180431365967, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement merely advocates for freedom of expression without containing any offensive content.\"\n}", "text": "The students should be able to express themselves .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and implies racial superiority, thus expressing hatred and discrimination.", "processing_time": 0.933264970779419, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and implies racial superiority, thus expressing hatred and discrimination.\"\n}", "text": "Americans and Irish understand each other better than Spaniards and the beaners .. the US and Ireland are the 1st world so they are educated unlike the beaners .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content directed at a specific group", "processing_time": 0.7832119464874268, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content directed at a specific group\"\n}", "text": "They have properly learnt their Zionist lesson ...", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text mentions various holidays but does not show any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1360032558441162, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text mentions various holidays but does not show any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I remember when I was at my college during the Holidays ... every single holiday would be mentioned loud and clear except for Christmas ... especially kwanzaa .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses an opinion on media bias without containing explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.102673053741455, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses an opinion on media bias without containing explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "great posts. see how the media has been lying to us for decades about what actually happened and what it was actually all about .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech; it's a question about a method.", "processing_time": 0.9848411083221436, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech; it's a question about a method.\"\n}", "text": "Has anyone claiming this method been able to make even one block like that with wedges water and copper tools ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist and threatening language", "processing_time": 0.7016682624816895, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist and threatening language\"\n}", "text": "They will eventually kill off the whites and go back to the jungle ways .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a speculative joke with no explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0709657669067383, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a speculative joke with no explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Wasn't there something like a big bang in which a burrito exploded and all the beans flew everywhere and started reproducing exponentially ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is neutral and does not contain any offensive content.", "processing_time": 0.8691248893737793, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is neutral and does not contain any offensive content.\"\n}", "text": "Apparently young kids pick languages up very quickly .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racist stereotypes and derogatory implications", "processing_time": 0.7632670402526855, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racist stereotypes and derogatory implications\"\n}", "text": "you just say blacks hate the cold. the only reason they are good athletes is because when you have to run lions and tigers you get fast or eaten .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and derogatory content targeting a specific group.", "processing_time": 0.8599185943603516, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and derogatory content targeting a specific group.\"\n}", "text": "why did god give negroes good rhythm ?? because he messed up their lips , hair , nose and skin so yes they are ugly !!!!!!!!!!!!!!", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about religious texts without using explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.1215229034423828, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about religious texts without using explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "I believe <PERSON> 's faith was tested by <PERSON> when he asked him to sacrifice <PERSON> , the quran falsely claims it was <PERSON><PERSON><PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content directed at a specific group", "processing_time": 0.7823464870452881, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content directed at a specific group\"\n}", "text": "I take it you are a little person then .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful language targeting a specific group", "processing_time": 0.8321342468261719, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful language targeting a specific group\"\n}", "text": "Like the parasites they are they simply moved to a new host , to continue their destruction of Western White civilization and culture", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech; it appears to be a question with no derogatory or threatening language.", "processing_time": 1.0975558757781982, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech; it appears to be a question with no derogatory or threatening language.\"\n}", "text": "I'm too lazy to reread , did he say ' Teacher `` or '' Teacher 's asst . \" ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and implies violence against a specific ethnic group", "processing_time": 0.8867006301879883, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and implies violence against a specific ethnic group\"\n}", "text": "I 'm sure I would have done great in public school if the Mexican gang members stopped kicking my * * * long enough for me to get my homework done ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and insulting language towards non-Jewish people (goyim).", "processing_time": 0.9829225540161133, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and insulting language towards non-Jewish people (goyim).\"\n}", "text": "<PERSON> 's own divine intervention , aided by Jewish money swindled from the goyim .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'JAP' which is considered offensive", "processing_time": 0.9048349857330322, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'JAP' which is considered offensive\"\n}", "text": "Had a run in with a JAP tonight .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit threat and hatred towards a specific individual", "processing_time": 0.7890515327453613, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit threat and hatred towards a specific individual\"\n}", "text": "I seriously hope she dies in a fire one day .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory content towards black sports players", "processing_time": 0.8202087879180908, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory content towards black sports players\"\n}", "text": "Just watch them at North America and they like all black sport players and their `` slang '' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0155510902404785, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "And all those holes below 40,000 feet are filled with oil instead of water .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This text criticizes economic policies without using explicit insults, threats, hatred, or derogatory language.", "processing_time": 1.****************, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This text criticizes economic policies without using explicit insults, threats, hatred, or derogatory language.\"\n}", "text": "Agreed! rep will have to wait though .. Cheap money , massive bailouts of crony banksters , manipulated employment figures , etc , etc , ..", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial insensitivity and implicit bias against a specific race", "processing_time": 0.****************, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial insensitivity and implicit bias against a specific race\"\n}", "text": "I bet they wouldnt have done that crap to a black person that had black power or those black fists that the panthers use written on his back pack .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text provides information about an educational institution without any offensive content.", "processing_time": 0.****************, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text provides information about an educational institution without any offensive content.\"\n}", "text": "www.sjca.edu St. John 's College , Santa Fe , N.M.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains hateful and genocidal content directed towards other races", "processing_time": 0.8650591373443604, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains hateful and genocidal content directed towards other races\"\n}", "text": "agreed, there is no reason to make genocide of other races as long as they are in another country and have nothing to do with our affairs !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This sentence appears to express positive sentiment about joining a unit known for its spirit and effectiveness.", "processing_time": 1.0109972953796387, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This sentence appears to express positive sentiment about joining a unit known for its spirit and effectiveness.\"\n}", "text": "You are joining a unit that is famous for its great spirit and effectiveness at awakening our kinsmen .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive content detected; statement appears neutral.", "processing_time": 0.7474765777587891, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive content detected; statement appears neutral.\"\n}", "text": "Enjoy your classroom .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains aggressive language and insinuations of physical violence, which could be perceived as threatening.", "processing_time": 1.0046920776367188, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains aggressive language and insinuations of physical violence, which could be perceived as threatening.\"\n}", "text": "you gotta stand up for yourself you cant just sit back and take their * * * * but on the other hand you cant go out looking for it either but if it comes down to a fight dont back down", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory language targeting multiple groups", "processing_time": 0.7847671508789062, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language targeting multiple groups\"\n}", "text": "God forbid anyone should promote straight racially loyal hetrosexuality <PERSON><PERSON><PERSON>", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.9951295852661133, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "First off they probably wont do anything , second off dont start anything .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0060696601867676, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "- = <PERSON>lfi = -", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement only mentions personal viewing habits and does not contain offensive content.", "processing_time": 0.9178576469421387, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement only mentions personal viewing habits and does not contain offensive content.\"\n}", "text": "Sometimes I do watch shows about WWII on the History channel but other than that I never watch it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains racial slurs and derogatory language targeting specific groups.", "processing_time": 0.8787760734558105, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains racial slurs and derogatory language targeting specific groups.\"\n}", "text": "LOL GJ brother 10 points for the Uber humans and - 10 for the sub human ape creatures you are truly one bad mofo whitey crakker man", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is speculative and does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.0665695667266846, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is speculative and does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "They probably stole a bunch of stuff .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement discusses personal experience but does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1024677753448486, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement discusses personal experience but does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "it is better with a shaved head.A friend of mine was beaten really bad a year ago because they could have pulled him by his hair .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text appears to be using profanity but does not target specific groups or individuals with hatred or insults.", "processing_time": 1.0760929584503174, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text appears to be using profanity but does not target specific groups or individuals with hatred or insults.\"\n}", "text": "damn! at least that sounds more plausable than a stupid sea. maybe look on ebay ??? are u sure there is none left ???", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and insinuation of guilt based on race", "processing_time": 0.8937149047851562, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and insinuation of guilt based on race\"\n}", "text": "Its simple , they want to create white guilt , .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit endorsement of a group associated with hate and discrimination", "processing_time": 0.8487231731414795, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit endorsement of a group associated with hate and discrimination\"\n}", "text": "Welcome to Stormfront , the largest White Nationalist community on the web .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory implications and threats towards the individual's educational experience", "processing_time": 0.8742861747741699, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory implications and threats towards the individual's educational experience\"\n}", "text": "I know how you feel BreakTheSilence luckily this is your last year in school and it is almost over otherwise I would say you should look into independent study where you do not have to put up with all the filth that is in the public education system .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.8593544960021973, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I am going to try and update my site soon , I did however add a chat room .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content found.", "processing_time": 0.8687038421630859, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content found.\"\n}", "text": "I've told my friends and all of them agree with me ( some only partially ) .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults and derogatory content directed towards a specific racial group.", "processing_time": 0.9229118824005127, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults and derogatory content directed towards a specific racial group.\"\n}", "text": "It makes my flesh crawl when I see white girls having to be felt up as they walk down the hallways by big lipped spear chuckers. : attack", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text provides information about a resource center for educational materials without expressing offensive content.", "processing_time": 0.9615647792816162, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text provides information about a resource center for educational materials without expressing offensive content.\"\n}", "text": "You could try looking at the National socialist and Fascist Resource Centre at http://www.ufc.co.uk They cover a whole range of educational material .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.", "processing_time": 1.0188848972320557, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No explicit insults, threats, hatred or derogatory content towards specific groups or individuals.\"\n}", "text": "American Media appears to have a blackout on these developments .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist language targeting 'Whites'", "processing_time": 0.8211832046508789, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist language targeting 'Whites'\"\n}", "text": "And now this clip suggests they were suffering by having to live among heartless and barbarian Whites .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and accusatory language targeting a specific group", "processing_time": 0.8791449069976807, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and accusatory language targeting a specific group\"\n}", "text": "Apparently the Jews ca n't take it when the truth is spoken about them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement merely presents a viewpoint on dietary choices without expressing offense.", "processing_time": 0.86639404296875, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement merely presents a viewpoint on dietary choices without expressing offense.\"\n}", "text": "Whether or not one chooses to eat meat is a very personal decision .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "This statement criticizes financial decisions without using offensive language or targeting specific groups.", "processing_time": 0.9184637069702148, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"This statement criticizes financial decisions without using offensive language or targeting specific groups.\"\n}", "text": "the money he is giving away to refugees and the third world is money Canada does not even have !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.983710527420044, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "Now I get it !", "true_label": 0, "dataset": "HateSpeechStormfront"}]}