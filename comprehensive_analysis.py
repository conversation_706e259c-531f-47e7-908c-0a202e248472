#!/usr/bin/env python3
"""
综合分析脚本 - 对比新旧版本的评估结果
分析数据集特点和框架不足，提供优化建议
"""

import json
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

def load_log_file(file_path):
    """加载日志文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_metrics(log_data):
    """提取关键指标"""
    if not log_data:
        return {}
    
    metrics = log_data.get('metrics', {})
    metadata = log_data.get('metadata', {})
    
    return {
        'accuracy': metrics.get('accuracy', 0),
        'precision': metrics.get('precision', 0),
        'recall': metrics.get('recall', 0),
        'f1': metrics.get('f1', 0),
        'avg_processing_time': metrics.get('avg_processing_time', 0),
        'dataset': metadata.get('dataset', 'unknown'),
        'system_type': metadata.get('system_type', 'unknown'),
        'num_samples': metadata.get('num_samples', 0)
    }

def analyze_layer_usage(log_data):
    """分析层级使用情况"""
    if not log_data or 'results' not in log_data:
        return {}
    
    results = log_data['results']
    total = len(results)
    layer2_triggered = sum(1 for r in results if r.get('layer2_triggered', False))
    
    return {
        'total_samples': total,
        'layer2_triggered': layer2_triggered,
        'layer2_ratio': layer2_triggered / total if total > 0 else 0,
        'layer1_only': total - layer2_triggered,
        'layer1_ratio': (total - layer2_triggered) / total if total > 0 else 0
    }

def analyze_ambiguity_patterns(log_data):
    """分析歧义检测模式"""
    if not log_data or 'results' not in log_data:
        return {}
    
    results = log_data['results']
    ambiguity_scores = []
    ambiguity_reasons = []
    
    for result in results:
        score = result.get('ambiguity_score', 0)
        ambiguity_scores.append(score)
        
        reasons = result.get('ambiguity_reasons', [])
        ambiguity_reasons.extend(reasons)
    
    reason_counts = Counter(ambiguity_reasons)
    
    return {
        'avg_ambiguity_score': np.mean(ambiguity_scores) if ambiguity_scores else 0,
        'max_ambiguity_score': np.max(ambiguity_scores) if ambiguity_scores else 0,
        'std_ambiguity_score': np.std(ambiguity_scores) if ambiguity_scores else 0,
        'reason_distribution': dict(reason_counts),
        'high_ambiguity_count': len([s for s in ambiguity_scores if s > 0.7]),
        'medium_ambiguity_count': len([s for s in ambiguity_scores if 0.3 < s <= 0.7]),
        'low_ambiguity_count': len([s for s in ambiguity_scores if s <= 0.3])
    }

def analyze_weight_learning(log_data):
    """分析权重学习效果"""
    if not log_data or 'results' not in log_data:
        return {}
    
    results = log_data['results']
    toxigen_weights = []
    hurtlex_weights = []
    weight_updates = 0
    
    for result in results:
        layer1_results = result.get('layer1_results', {})
        fusion_result = layer1_results.get('fusion_result', {})
        
        if fusion_result:
            toxigen_weight = fusion_result.get('toxigen_weight', 0.5)
            hurtlex_weight = fusion_result.get('hurtlex_weight', 0.5)
            weight_updated = fusion_result.get('weight_updated', False)
            
            toxigen_weights.append(toxigen_weight)
            hurtlex_weights.append(hurtlex_weight)
            
            if weight_updated:
                weight_updates += 1
    
    if not toxigen_weights:
        return {}
    
    return {
        'initial_toxigen': toxigen_weights[0],
        'final_toxigen': toxigen_weights[-1],
        'toxigen_change': toxigen_weights[-1] - toxigen_weights[0],
        'toxigen_std': np.std(toxigen_weights),
        'weight_updates': weight_updates,
        'update_ratio': weight_updates / len(results) if results else 0,
        'avg_toxigen': np.mean(toxigen_weights),
        'avg_hurtlex': np.mean(hurtlex_weights)
    }

def analyze_decision_patterns(log_data):
    """分析决策模式"""
    if not log_data or 'results' not in log_data:
        return {}
    
    results = log_data['results']
    decision_paths = []
    confidence_distribution = []
    
    for result in results:
        # 决策路径
        if 'fusion_details' in result:
            path = result['fusion_details'].get('decision_path', 'unknown')
            decision_paths.append(path)
        
        # 置信度分布
        confidence = result.get('confidence', 0)
        confidence_distribution.append(confidence)
    
    path_counts = Counter(decision_paths)
    
    return {
        'decision_path_distribution': dict(path_counts),
        'avg_confidence': np.mean(confidence_distribution) if confidence_distribution else 0,
        'confidence_std': np.std(confidence_distribution) if confidence_distribution else 0,
        'high_confidence_count': len([c for c in confidence_distribution if c > 0.8]),
        'low_confidence_count': len([c for c in confidence_distribution if c < 0.6])
    }

def analyze_error_patterns(log_data):
    """分析错误模式"""
    if not log_data or 'results' not in log_data:
        return {}
    
    results = log_data['results']
    errors = {
        'false_positives': [],  # 预测为1，实际为0
        'false_negatives': [],  # 预测为0，实际为1
        'correct_positives': [],  # 预测为1，实际为1
        'correct_negatives': []   # 预测为0，实际为0
    }
    
    for result in results:
        predicted = result.get('verdict', 0)
        actual = result.get('true_label', 0)
        text = result.get('text', '')
        
        if predicted == 1 and actual == 0:
            errors['false_positives'].append(text)
        elif predicted == 0 and actual == 1:
            errors['false_negatives'].append(text)
        elif predicted == 1 and actual == 1:
            errors['correct_positives'].append(text)
        elif predicted == 0 and actual == 0:
            errors['correct_negatives'].append(text)
    
    return {
        'false_positive_count': len(errors['false_positives']),
        'false_negative_count': len(errors['false_negatives']),
        'false_positive_examples': errors['false_positives'][:3],  # 前3个例子
        'false_negative_examples': errors['false_negatives'][:3],
        'error_rate': (len(errors['false_positives']) + len(errors['false_negatives'])) / len(results) if results else 0
    }

def main():
    """主分析函数"""
    print("=" * 80)
    print("综合分析报告：新旧版本对比")
    print("=" * 80)
    
    # 定义文件路径
    datasets = ['ImplicitHate', 'HateSpeechOffensive', 'HateSpeechStormfront']
    
    current_files = {
        'ImplicitHate': 'logs/twolayerdetectionsystem_gpt-3.5-turbo-0125_ImplicitHate_log_96877883_20250804080601.json',
        'HateSpeechOffensive': 'logs/twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechOffensive_log_5349811a_20250804080726.json',
        'HateSpeechStormfront': 'logs/twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechStormfront_log_9bd1b034_20250804075458.json'
    }
    
    previous_files = {
        'ImplicitHate': 'previous_log/twolayerdetectionsystem_gpt-3.5-turbo-0125_ImplicitHate_log_11c572f5_20250802180646.json',
        'HateSpeechOffensive': 'previous_log/twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechOffensive_log_fc9adb7b_20250802180617.json',
        'HateSpeechStormfront': 'previous_log/twolayerdetectionsystem_gpt-3.5-turbo-0125_HateSpeechStormfront_log_30082e77_20250802174702.json'
    }
    
    # 加载所有数据
    current_data = {}
    previous_data = {}
    
    for dataset in datasets:
        current_data[dataset] = load_log_file(current_files[dataset])
        previous_data[dataset] = load_log_file(previous_files[dataset])
    
    # 1. 性能指标对比
    print("\n1. 性能指标对比")
    print("-" * 50)
    
    performance_comparison = []
    for dataset in datasets:
        current_metrics = extract_metrics(current_data[dataset])
        previous_metrics = extract_metrics(previous_data[dataset])
        
        performance_comparison.append({
            '数据集': dataset,
            '版本': '当前版本',
            '准确率': f"{current_metrics['accuracy']:.4f}",
            'F1分数': f"{current_metrics['f1']:.4f}",
            '精确率': f"{current_metrics['precision']:.4f}",
            '召回率': f"{current_metrics['recall']:.4f}",
            '处理时间': f"{current_metrics['avg_processing_time']:.3f}s"
        })
        
        performance_comparison.append({
            '数据集': dataset,
            '版本': '历史版本',
            '准确率': f"{previous_metrics['accuracy']:.4f}",
            'F1分数': f"{previous_metrics['f1']:.4f}",
            '精确率': f"{previous_metrics['precision']:.4f}",
            '召回率': f"{previous_metrics['recall']:.4f}",
            '处理时间': f"{previous_metrics['avg_processing_time']:.3f}s"
        })
        
        # 计算性能变化
        f1_change = current_metrics['f1'] - previous_metrics['f1']
        acc_change = current_metrics['accuracy'] - previous_metrics['accuracy']
        
        print(f"\n{dataset}:")
        print(f"  F1分数变化: {f1_change:+.4f}")
        print(f"  准确率变化: {acc_change:+.4f}")
        if f1_change < 0:
            print(f"  ⚠️ 性能下降")
        else:
            print(f"  ✅ 性能提升")
    
    df_performance = pd.DataFrame(performance_comparison)
    print(f"\n{df_performance.to_string(index=False)}")
    
    # 2. 层级使用分析
    print("\n\n2. 层级使用分析")
    print("-" * 50)
    
    for dataset in datasets:
        current_layer = analyze_layer_usage(current_data[dataset])
        previous_layer = analyze_layer_usage(previous_data[dataset])
        
        print(f"\n{dataset}:")
        print(f"  当前版本第二层触发率: {current_layer.get('layer2_ratio', 0):.2%}")
        print(f"  历史版本第二层触发率: {previous_layer.get('layer2_ratio', 0):.2%}")
        
        ratio_change = current_layer.get('layer2_ratio', 0) - previous_layer.get('layer2_ratio', 0)
        print(f"  触发率变化: {ratio_change:+.2%}")
    
    # 3. 歧义检测分析
    print("\n\n3. 歧义检测分析")
    print("-" * 50)
    
    for dataset in datasets:
        current_ambiguity = analyze_ambiguity_patterns(current_data[dataset])
        previous_ambiguity = analyze_ambiguity_patterns(previous_data[dataset])
        
        print(f"\n{dataset}:")
        print(f"  当前版本平均歧义分数: {current_ambiguity.get('avg_ambiguity_score', 0):.4f}")
        print(f"  历史版本平均歧义分数: {previous_ambiguity.get('avg_ambiguity_score', 0):.4f}")
        
        print(f"  当前版本主要歧义原因:")
        current_reasons = current_ambiguity.get('reason_distribution', {})
        for reason, count in sorted(current_reasons.items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"    - {reason}: {count}")
    
    # 4. 权重学习分析
    print("\n\n4. 权重学习分析")
    print("-" * 50)
    
    for dataset in datasets:
        current_weights = analyze_weight_learning(current_data[dataset])
        previous_weights = analyze_weight_learning(previous_data[dataset])
        
        if current_weights and previous_weights:
            print(f"\n{dataset}:")
            print(f"  当前版本ToxiGen权重变化: {current_weights.get('toxigen_change', 0):+.4f}")
            print(f"  历史版本ToxiGen权重变化: {previous_weights.get('toxigen_change', 0):+.4f}")
            print(f"  当前版本权重更新率: {current_weights.get('update_ratio', 0):.2%}")
            print(f"  历史版本权重更新率: {previous_weights.get('update_ratio', 0):.2%}")
    
    # 5. 错误模式分析
    print("\n\n5. 错误模式分析")
    print("-" * 50)
    
    for dataset in datasets:
        current_errors = analyze_error_patterns(current_data[dataset])
        previous_errors = analyze_error_patterns(previous_data[dataset])
        
        print(f"\n{dataset}:")
        print(f"  当前版本错误率: {current_errors.get('error_rate', 0):.2%}")
        print(f"  历史版本错误率: {previous_errors.get('error_rate', 0):.2%}")
        print(f"  当前版本假阳性: {current_errors.get('false_positive_count', 0)}")
        print(f"  当前版本假阴性: {current_errors.get('false_negative_count', 0)}")
        
        if current_errors.get('false_negative_examples'):
            print(f"  典型假阴性例子:")
            for example in current_errors['false_negative_examples']:
                print(f"    - {example[:100]}...")
    
    # 6. 数据集特点分析
    print("\n\n6. 数据集特点分析")
    print("-" * 50)
    
    dataset_characteristics = {}
    for dataset in datasets:
        current_metrics = extract_metrics(current_data[dataset])
        current_layer = analyze_layer_usage(current_data[dataset])
        current_ambiguity = analyze_ambiguity_patterns(current_data[dataset])
        
        dataset_characteristics[dataset] = {
            'difficulty': 1 - current_metrics['f1'],  # F1越低，难度越高
            'ambiguity_level': current_ambiguity.get('avg_ambiguity_score', 0),
            'layer2_dependency': current_layer.get('layer2_ratio', 0),
            'performance': current_metrics['f1']
        }
    
    # 按难度排序
    sorted_datasets = sorted(dataset_characteristics.items(), key=lambda x: x[1]['difficulty'], reverse=True)
    
    print("数据集难度排序（从难到易）:")
    for i, (dataset, chars) in enumerate(sorted_datasets, 1):
        print(f"  {i}. {dataset}")
        print(f"     F1分数: {chars['performance']:.4f}")
        print(f"     歧义水平: {chars['ambiguity_level']:.4f}")
        print(f"     第二层依赖: {chars['layer2_dependency']:.2%}")
    
    # 7. 问题识别和优化建议
    print("\n\n7. 问题识别和优化建议")
    print("-" * 50)
    
    print("\n发现的主要问题:")
    
    # 分析性能下降的原因
    performance_declined = []
    for dataset in datasets:
        current_f1 = extract_metrics(current_data[dataset])['f1']
        previous_f1 = extract_metrics(previous_data[dataset])['f1']
        if current_f1 < previous_f1:
            performance_declined.append((dataset, previous_f1 - current_f1))
    
    if performance_declined:
        print(f"\n1. 性能下降问题:")
        for dataset, decline in performance_declined:
            print(f"   - {dataset}: F1分数下降 {decline:.4f}")
    
    # 分析歧义检测问题
    print(f"\n2. 歧义检测问题:")
    for dataset in datasets:
        current_layer = analyze_layer_usage(current_data[dataset])
        layer2_ratio = current_layer.get('layer2_ratio', 0)
        if layer2_ratio > 0.8:
            print(f"   - {dataset}: 第二层触发率过高 ({layer2_ratio:.2%})")
        elif layer2_ratio < 0.3:
            print(f"   - {dataset}: 第二层触发率过低 ({layer2_ratio:.2%})")
    
    print(f"\n优化建议:")
    print(f"1. 歧义检测优化:")
    print(f"   - 调整歧义阈值，平衡第一层和第二层的使用")
    print(f"   - 优化置信度阈值检测策略")
    print(f"   - 改进模型分歧检测的敏感性")
    
    print(f"\n2. 权重学习优化:")
    print(f"   - 增加权重学习的样本窗口")
    print(f"   - 调整学习率和奖励机制")
    print(f"   - 考虑数据集特定的权重初始化")
    
    print(f"\n3. 模型集成优化:")
    print(f"   - 重新评估ToxiGen和HurtLex的权重分配")
    print(f"   - 考虑引入第三个基础模型")
    print(f"   - 优化模型输出的置信度校准")
    
    print(f"\n4. 数据集特定优化:")
    for dataset, chars in dataset_characteristics.items():
        if chars['difficulty'] > 0.3:  # 高难度数据集
            print(f"   - {dataset}: 考虑增加特定的预处理或后处理步骤")

if __name__ == "__main__":
    main()
