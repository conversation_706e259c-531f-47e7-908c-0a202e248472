import os
import time
import json
import uuid
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import seaborn as sns
from .data_loader import get_dataset_loader

class ModelEvaluator:
    """模型评估器"""
    def __init__(self, detector, dataset_name, num_samples=10, start_idx=0):
        """
        初始化评估器
        
        参数:
        - detector: 检测器实例
        - dataset_name: 数据集名称
        - num_samples: 样本数量
        - start_idx: 起始索引
        """
        
        self.detector = detector
        self.dataset_name = dataset_name
        self.num_samples = num_samples
        self.start_idx = start_idx
        
        # 生成唯一运行ID
        self.run_id = str(uuid.uuid4())[:8]
        self.timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 创建日志目录
        self.log_dir = "logs"
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 创建结果目录
        self.results_dir = "results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 加载数据集
        self.data_loader = get_dataset_loader(dataset_name)
        self.test_data = self.data_loader.get_sample(num_samples, start_idx)
        
        # 存储评估结果
        self.all_results = []
        self.processing_times = []
    
    def evaluate(self):
        """评估模型性能"""
        detector_type = self.detector.__class__.__name__
        print(f"Starting evaluation of {detector_type} on {self.dataset_name} dataset (Run ID: {self.run_id})")
        print(f"Loaded {len(self.test_data)} test samples")
        
        for idx, sample in enumerate(self.test_data):
            # Correctly show the 1-based absolute index
            current_sample_num = self.start_idx + idx + 1
            print(f"\nProcessing sample {current_sample_num}/{self.start_idx + len(self.test_data)}...")
            text = sample["text"]
            true_label = sample["label"]
            
            print(f"Text: {text}")
            print(f"True label: {true_label}")
            
            # 记录处理时间
            start_time = time.time()
            
            # 使用检测器进行检测
            if hasattr(self.detector, 'detect'):
                # 检查是否是两层系统
                if hasattr(self.detector, 'get_statistics'):
                    # 两层系统，传递数据集名称和真实标签（用于动态权重学习）
                    result = self.detector.detect(text, dataset_name=self.dataset_name, true_label=true_label)
                else:
                    # 单一智能体
                    result = self.detector.detect(text)
            else:
                result = {"verdict": 0, "confidence": 0.5, "error": "No detect method found"}

            # 计算处理时间
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)

            # 处理两层系统的结果格式
            if hasattr(result, 'verdict'):
                # 如果是DetectionResult对象，转换为字典
                result_dict = {
                    "verdict": result.verdict,
                    "confidence": result.confidence,
                    "processing_time": result.processing_time,
                    "layer2_triggered": getattr(result, 'layer2_triggered', False),
                    "ambiguity_score": getattr(result, 'ambiguity_score', 0.0),
                    "layer1_results": {
                        "fusion_result": getattr(result, 'layer1_result', {}),
                        "toxigen_result": getattr(result, 'layer1_toxigen_result', {}),
                        "hurtlex_result": getattr(result, 'layer1_hurtlex_result', {})
                    },
                    "layer2_results": {
                        "single_result": getattr(result, 'layer2_single_result', {}),
                        "retrieval_result": getattr(result, 'layer2_retrieval_result', {})
                    },
                    "reasoning": getattr(result, 'decision_path', ''),
                    "ambiguity_reasons": getattr(result, 'ambiguity_reasons', []),
                    "is_ambiguous": getattr(result, 'is_ambiguous', False)
                }
                result = result_dict

            # 添加原始文本和真实标签
            result["text"] = text
            result["true_label"] = true_label
            result["processing_time"] = processing_time
            result["dataset"] = self.dataset_name

            # 显示检测结果
            verdict = result.get("verdict", 0)
            confidence = result.get("confidence", 0.0)
            print(f"Prediction: {verdict} (confidence: {confidence:.3f})")
            print(f"Processing time: {processing_time:.3f}s")

            # 如果是两层系统，显示额外信息
            if result.get("layer2_triggered", False):
                print(f"Layer 2 triggered (ambiguity score: {result.get('ambiguity_score', 0.0):.3f})")
            else:
                print("Resolved in Layer 1")

            # 保存结果
            self.all_results.append(result)
        
        # 计算评估指标
        metrics = self._calculate_metrics()
        
        # 获取检测器类型和模型信息
        detector_type = self.detector.__class__.__name__
        model_name = getattr(self.detector, 'model', 'unknown')

        # 确保model_name是字符串
        if not isinstance(model_name, str):
            model_name = detector_type.lower()

        # 检查是否是两层系统
        is_two_layer = hasattr(self.detector, 'get_statistics')

        # 构建最终的日志内容
        log_data = {
            "metadata": {
                "run_id": self.run_id,
                "dataset": self.dataset_name,
                "model": model_name,
                "detector_type": detector_type,
                "system_type": "two_layer" if is_two_layer else "single_agent",
                "num_samples": self.num_samples,
                "start_idx": self.start_idx,
                "created_at": datetime.now().isoformat()
            },
            "metrics": metrics,
            "results": self.all_results
        }

        # 如果是两层系统，添加系统统计信息
        if is_two_layer:
            system_stats = self.detector.get_statistics()
            log_data["system_statistics"] = system_stats
            
        # 保存结果到日志文件
        detector_type_clean = detector_type.lower().replace("agent", "").replace("detector", "")

        # 使用安全的模型名称
        model_name_cleaned = model_name.replace('/', '_').replace('\\', '_')
        # 移除Windows系统不允许的字符（: * ? " < > |）
        model_name_cleaned = model_name_cleaned.replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
        log_file = os.path.join(self.log_dir, f"{detector_type_clean}_{model_name_cleaned}_{self.dataset_name}_log_{self.run_id}_{self.timestamp}.json")
        
        # 自定义JSON编码器处理不可序列化的对象
        def json_serializer(obj):
            if isinstance(obj, set):
                return list(obj)
            elif hasattr(obj, '__dict__'):
                return str(obj)
            else:
                return str(obj)

        with open(log_file, "w", encoding="utf-8") as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2, default=json_serializer)
        
        print(f"\nResults saved to: {log_file}")
        
        # 打印评估指标
        self._print_metrics(metrics)
        
        return metrics, self.all_results
    
    def _calculate_metrics(self):
        """计算评估指标"""
        # 提取预测标签和真实标签
        y_true = []
        y_pred = []
        
        for result in self.all_results:
            true_label = result["true_label"]
            
            if "verdict" in result:
                pred_label = result["verdict"]
            elif "final_verdict" in result:
                pred_label = result["final_verdict"]
            else:
                # 如果没有预测标签，跳过
                continue
            
            if pred_label is not None:
                try:
                    y_true.append(int(true_label))
                    y_pred.append(int(pred_label))
                except (ValueError, TypeError):
                    # 如果转换失败，跳过这个样本
                    print(f"警告: 无法将标签转换为整数。真实标签: {true_label}, 预测标签: {pred_label}。跳过此样本。")
                    continue
        
        if not y_true or not y_pred:
            return {
                "accuracy": 0,
                "precision": 0,
                "recall": 0,
                "f1": 0,
                "confusion_matrix": [[0, 0], [0, 0]],
                "avg_processing_time": sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
            }
        
        # 计算指标
        accuracy = accuracy_score(y_true, y_pred)
        
        # 处理可能的警告：precision/recall/f1 undefined when no positive samples
        if len(set(y_true)) == 1 and y_true[0] == 0:
            precision = 0
            recall = 0
            f1 = 0
        else:
            precision = precision_score(y_true, y_pred, zero_division=0)
            recall = recall_score(y_true, y_pred, zero_division=0)
            f1 = f1_score(y_true, y_pred, zero_division=0)
        
        cm = confusion_matrix(y_true, y_pred, labels=[0, 1]).tolist()
        
        return {
            "accuracy": accuracy,
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "confusion_matrix": cm,
            "avg_processing_time": sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
        }
    
    def _print_metrics(self, metrics):
        """打印评估指标"""
        print(f"\n{self.dataset_name} Dataset Evaluation Results:")
        print(f"Accuracy: {metrics['accuracy']:.4f}")
        print(f"Precision: {metrics['precision']:.4f}")
        print(f"Recall: {metrics['recall']:.4f}")
        print(f"F1 Score: {metrics['f1']:.4f}")
        print("Confusion Matrix:")
        print(metrics['confusion_matrix'])
        print(f"Average processing time: {metrics['avg_processing_time']:.2f} seconds")
    
    def plot_metrics(self, other_detector_metrics=None, other_detector_name=None):
        """绘制评估指标图表"""
        sns.set_style("whitegrid")
        detector_type = self.detector.__class__.__name__.lower().replace("detector", "")

        metrics_to_plot = ["accuracy", "precision", "recall", "f1"]
        values1 = [self._calculate_metrics()[m] for m in metrics_to_plot]

        if other_detector_metrics:
            values2 = [other_detector_metrics[m] for m in metrics_to_plot]

            fig, ax = plt.subplots(figsize=(12, 7))
            bar_width = 0.35
            index = np.arange(len(metrics_to_plot))

            bar1 = ax.bar(index, values1, bar_width, label=detector_type)
            bar2 = ax.bar(index + bar_width, values2, bar_width, label=other_detector_name)

            ax.set_xlabel('Metrics', fontsize=12)
            ax.set_ylabel('Scores', fontsize=12)
            ax.set_title(f'Metrics Comparison: {detector_type} vs {other_detector_name} on {self.dataset_name}', fontsize=14)
            ax.set_xticks(index + bar_width / 2)
            ax.set_xticklabels(metrics_to_plot)
            ax.legend()
            fig.tight_layout()
            plot_file = os.path.join(self.results_dir, f"{self.dataset_name}_comparison_{self.run_id}.png")
            print(f"Comparison chart saved to: {plot_file}")
        else:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.bar(metrics_to_plot, values1, color=['skyblue', 'lightgreen', 'salmon', 'gold'])
            ax.set_ylabel('Scores')
            ax.set_title(f'{self.dataset_name} Dataset Evaluation Results ({detector_type})')
            ax.set_ylim(0, 1.1)

            for i, v in enumerate(values1):
                ax.text(i, v + 0.02, f"{v:.4f}", ha='center', fontweight='bold')
            
            fig.tight_layout()
            plot_file = os.path.join(self.results_dir, f"{self.dataset_name}_{detector_type}_{self.run_id}.png")
            print(f"Chart saved to: {plot_file}")

        plt.savefig(plot_file)
        plt.close(fig) # Prevent plot from displaying 