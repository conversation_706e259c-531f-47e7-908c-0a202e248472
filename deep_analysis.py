#!/usr/bin/env python3
"""
深入分析新旧版本的具体差异和问题
"""

import json
import os
import pandas as pd
from pathlib import Path
import numpy as np
from collections import defaultdict
import matplotlib.pyplot as plt

def load_and_analyze_detailed_results(log_file):
    """加载并分析详细结果"""
    with open(log_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = data.get('results', [])
    if not results:
        return None
    
    analysis = {
        'metadata': data.get('metadata', {}),
        'metrics': data.get('metrics', {}),
        'sample_count': len(results),
        'layer2_triggers': 0,
        'ambiguity_scores': [],
        'confidences': [],
        'processing_times': [],
        'decision_paths': defaultdict(int),
        'ambiguity_distribution': {'low': 0, 'medium': 0, 'high': 0}
    }
    
    for result in results:
        # Layer 2 触发统计
        if result.get('layer2_triggered', False):
            analysis['layer2_triggers'] += 1
        
        # 置信度和歧义分数
        if 'confidence' in result:
            analysis['confidences'].append(result['confidence'])
        
        if 'ambiguity_score' in result:
            score = result['ambiguity_score']
            analysis['ambiguity_scores'].append(score)
            
            # 歧义分布
            if score < 0.3:
                analysis['ambiguity_distribution']['low'] += 1
            elif score < 0.6:
                analysis['ambiguity_distribution']['medium'] += 1
            else:
                analysis['ambiguity_distribution']['high'] += 1
        
        # 处理时间
        if 'processing_time' in result:
            analysis['processing_times'].append(result['processing_time'])
        
        # 决策路径
        if 'layer1_results' in result and 'fusion_result' in result['layer1_results']:
            path = result['layer1_results']['fusion_result'].get('decision_path', 'unknown')
            analysis['decision_paths'][path] += 1
    
    # 计算统计量
    analysis['layer2_trigger_rate'] = analysis['layer2_triggers'] / analysis['sample_count']
    analysis['avg_confidence'] = np.mean(analysis['confidences']) if analysis['confidences'] else 0
    analysis['std_confidence'] = np.std(analysis['confidences']) if analysis['confidences'] else 0
    analysis['avg_ambiguity'] = np.mean(analysis['ambiguity_scores']) if analysis['ambiguity_scores'] else 0
    analysis['std_ambiguity'] = np.std(analysis['ambiguity_scores']) if analysis['ambiguity_scores'] else 0
    analysis['avg_processing_time'] = np.mean(analysis['processing_times']) if analysis['processing_times'] else 0
    
    return analysis

def compare_versions():
    """对比两个版本的详细差异"""
    print("=" * 80)
    print("深入分析：新旧版本详细对比")
    print("=" * 80)
    
    # 获取所有文件对
    current_files = list(Path('logs').glob('twolayerdetectionsystem_*.json'))
    previous_files = list(Path('previous_log').glob('twolayerdetectionsystem_*.json'))
    
    comparisons = []
    
    for curr_file in current_files:
        # 找到对应的旧版本文件
        model_dataset = '_'.join(curr_file.stem.split('_')[1:3])  # 提取模型和数据集
        
        prev_file = None
        for pf in previous_files:
            if model_dataset in pf.stem:
                prev_file = pf
                break
        
        if prev_file is None:
            continue
        
        print(f"\n分析: {model_dataset}")
        print("-" * 60)
        
        # 分析两个版本
        curr_analysis = load_and_analyze_detailed_results(curr_file)
        prev_analysis = load_and_analyze_detailed_results(prev_file)
        
        if curr_analysis is None or prev_analysis is None:
            print("数据加载失败")
            continue
        
        # 性能对比
        curr_metrics = curr_analysis['metrics']
        prev_metrics = prev_analysis['metrics']
        
        print("性能变化:")
        for metric in ['accuracy', 'precision', 'recall', 'f1']:
            curr_val = curr_metrics.get(metric, 0)
            prev_val = prev_metrics.get(metric, 0)
            diff = curr_val - prev_val
            print(f"  {metric}: {prev_val:.4f} -> {curr_val:.4f} ({diff:+.4f})")
        
        # Layer 2 触发率变化
        curr_trigger_rate = curr_analysis['layer2_trigger_rate']
        prev_trigger_rate = prev_analysis['layer2_trigger_rate']
        trigger_diff = curr_trigger_rate - prev_trigger_rate
        
        print(f"\nLayer 2 触发率: {prev_trigger_rate:.4f} -> {curr_trigger_rate:.4f} ({trigger_diff:+.4f})")
        
        # 置信度分布变化
        print(f"平均置信度: {prev_analysis['avg_confidence']:.4f} -> {curr_analysis['avg_confidence']:.4f}")
        print(f"置信度标准差: {prev_analysis['std_confidence']:.4f} -> {curr_analysis['std_confidence']:.4f}")
        
        # 歧义分数变化
        print(f"平均歧义分数: {prev_analysis['avg_ambiguity']:.4f} -> {curr_analysis['avg_ambiguity']:.4f}")
        print(f"歧义分数标准差: {prev_analysis['std_ambiguity']:.4f} -> {curr_analysis['std_ambiguity']:.4f}")
        
        # 歧义分布变化
        print("\n歧义分布变化:")
        for level in ['low', 'medium', 'high']:
            curr_count = curr_analysis['ambiguity_distribution'][level]
            prev_count = prev_analysis['ambiguity_distribution'][level]
            curr_pct = curr_count / curr_analysis['sample_count'] * 100
            prev_pct = prev_count / prev_analysis['sample_count'] * 100
            print(f"  {level}: {prev_pct:.1f}% -> {curr_pct:.1f}% ({curr_pct-prev_pct:+.1f}%)")
        
        # 决策路径变化
        print("\n主要决策路径变化:")
        all_paths = set(curr_analysis['decision_paths'].keys()) | set(prev_analysis['decision_paths'].keys())
        for path in sorted(all_paths):
            curr_count = curr_analysis['decision_paths'].get(path, 0)
            prev_count = prev_analysis['decision_paths'].get(path, 0)
            curr_pct = curr_count / curr_analysis['sample_count'] * 100
            prev_pct = prev_count / prev_analysis['sample_count'] * 100
            if abs(curr_pct - prev_pct) > 1:  # 只显示变化超过1%的路径
                print(f"  {path}: {prev_pct:.1f}% -> {curr_pct:.1f}% ({curr_pct-prev_pct:+.1f}%)")
        
        # 保存对比数据
        comparison = {
            'model_dataset': model_dataset,
            'curr_file': str(curr_file),
            'prev_file': str(prev_file),
            'performance_change': {
                'f1_change': curr_metrics.get('f1', 0) - prev_metrics.get('f1', 0),
                'accuracy_change': curr_metrics.get('accuracy', 0) - prev_metrics.get('accuracy', 0),
                'precision_change': curr_metrics.get('precision', 0) - prev_metrics.get('precision', 0),
                'recall_change': curr_metrics.get('recall', 0) - prev_metrics.get('recall', 0)
            },
            'behavioral_change': {
                'layer2_trigger_change': trigger_diff,
                'confidence_change': curr_analysis['avg_confidence'] - prev_analysis['avg_confidence'],
                'ambiguity_change': curr_analysis['avg_ambiguity'] - prev_analysis['avg_ambiguity']
            }
        }
        comparisons.append(comparison)
    
    return comparisons

def identify_root_causes(comparisons):
    """识别性能下降的根本原因"""
    print("\n" + "=" * 80)
    print("根本原因分析")
    print("=" * 80)

    # 分析模式
    patterns = {
        'confidence_drop': [],
        'layer2_overuse': [],
        'ambiguity_increase': [],
        'recall_drop': [],
        'ambiguity_distribution_shift': [],
        'calibration_issues': []
    }

    for comp in comparisons:
        model_dataset = comp['model_dataset']
        perf = comp['performance_change']
        behav = comp['behavioral_change']

        # 置信度下降模式
        if behav['confidence_change'] < -0.01:
            patterns['confidence_drop'].append((model_dataset, behav['confidence_change']))

        # Layer 2 过度使用
        if behav['layer2_trigger_change'] > 0.05:
            patterns['layer2_overuse'].append((model_dataset, behav['layer2_trigger_change']))

        # 歧义增加
        if behav['ambiguity_change'] > 0.01:
            patterns['ambiguity_increase'].append((model_dataset, behav['ambiguity_change']))

        # 召回率下降
        if perf['recall_change'] < -0.01:
            patterns['recall_drop'].append((model_dataset, perf['recall_change']))

    print("发现的问题模式:")

    if patterns['confidence_drop']:
        print(f"\n1. 置信度普遍下降 ({len(patterns['confidence_drop'])} 个案例):")
        for model_dataset, change in sorted(patterns['confidence_drop'], key=lambda x: x[1]):
            print(f"   {model_dataset}: {change:.4f}")

    if patterns['layer2_overuse']:
        print(f"\n2. Layer 2 过度触发 ({len(patterns['layer2_overuse'])} 个案例):")
        for model_dataset, change in sorted(patterns['layer2_overuse'], key=lambda x: x[1], reverse=True):
            print(f"   {model_dataset}: +{change:.4f}")

    if patterns['ambiguity_increase']:
        print(f"\n3. 歧义分数增加 ({len(patterns['ambiguity_increase'])} 个案例):")
        for model_dataset, change in sorted(patterns['ambiguity_increase'], key=lambda x: x[1], reverse=True):
            print(f"   {model_dataset}: +{change:.4f}")

    if patterns['recall_drop']:
        print(f"\n4. 召回率下降 ({len(patterns['recall_drop'])} 个案例):")
        for model_dataset, change in sorted(patterns['recall_drop'], key=lambda x: x[1]):
            print(f"   {model_dataset}: {change:.4f}")

    return patterns

def analyze_calibration_effects(comparisons):
    """分析置信度校准的具体影响"""
    print("\n" + "=" * 80)
    print("置信度校准影响分析")
    print("=" * 80)

    print("基于代码分析，新版本引入了以下校准机制:")
    print("1. 温度缩放 (Temperature Scaling)")
    print("2. 自适应阈值管理 (Adaptive Threshold Management)")
    print("3. 不确定性量化 (Uncertainty Quantification)")
    print("4. 增强歧义检测 (Enhanced Ambiguity Detection)")

    print("\n观察到的问题:")
    print("1. 歧义分数异常下降: 从0.57-0.68降到0.36-0.39")
    print("2. 高歧义样本完全消失: 从31.6%-68.8%降到0%")
    print("3. Layer 2触发率异常增加: +5.65%到+7.40%")
    print("4. 置信度普遍下降: -0.0106到-0.0166")

    print("\n推测的根本原因:")
    print("1. 温度缩放过度校准，导致置信度分布过于保守")
    print("2. 歧义检测阈值(0.3)与校准后的分布不匹配")
    print("3. 校准权重(0.6)和熵权重(0.4)的组合导致过度依赖校准结果")
    print("4. 动态阈值调整机制可能存在反馈循环问题")

def analyze_entropy_detection_issues(comparisons):
    """分析熵检测的问题"""
    print("\n" + "=" * 80)
    print("熵检测机制问题分析")
    print("=" * 80)

    print("基于代码分析，熵检测配置:")
    print("- entropy_threshold: 1.0")
    print("- ensemble_entropy_threshold: 0.8")
    print("- disagreement_threshold: 0.5")
    print("- 权重组合: entropy(0.3), disagreement(0.25), epistemic(0.25), aleatoric(0.2)")

    print("\n观察到的异常:")
    print("1. 歧义分数分布完全改变")
    print("2. 高歧义样本(>0.6)完全消失")
    print("3. 中等歧义样本(0.3-0.6)大幅增加")

    print("\n可能的问题:")
    print("1. 熵计算方法与实际模型输出不匹配")
    print("2. 贝叶斯不确定性估计可能存在偏差")
    print("3. 模型分歧度量可能过于严格")
    print("4. 综合评分公式可能需要重新校准")

if __name__ == "__main__":
    comparisons = compare_versions()
    patterns = identify_root_causes(comparisons)
    analyze_calibration_effects(comparisons)
    analyze_entropy_detection_issues(comparisons)

    # 保存详细分析结果
    with open('deep_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'comparisons': comparisons,
            'patterns': patterns
        }, f, indent=2, ensure_ascii=False)

    print(f"\n详细分析结果已保存到 deep_analysis_results.json")
