#!/usr/bin/env python3
"""
增强系统评估脚本
Enhanced System Evaluation Script

测试集成了自适应置信度校准和基于熵的歧义检测的双层多智能体系统

作者: AI Assistant
日期: 2025-08-04
版本: 1.0
"""

import argparse
import os
import sys
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core_modules.systems.two_layer_detection_system import TwoLayerDetectionSystem, TwoLayerConfig
from offensive_speech_detection.evaluator import ModelEvaluator
from offensive_speech_detection.data_loader import get_dataset_loader
from offensive_speech_detection.models import VectorDatabaseManager

class EnhancedSystemDetector:
    """增强系统检测器包装类，用于与评估器兼容"""
    
    def __init__(self, model="gpt-3.5-turbo-0125", provider="api", ollama_base_url="http://localhost:11434", 
                 local_model_path=None, dataset_name=None):
        """
        初始化增强系统检测器
        
        Args:
            model: 模型名称
            provider: 模型提供者 (api/ollama/local)
            ollama_base_url: Ollama API基础URL
            local_model_path: 本地模型路径
            dataset_name: 数据集名称
        """
        self.dataset_name = dataset_name
        self.model = model
        
        # 创建配置
        config = TwoLayerConfig()
        config.layer2_llm_model = model
        config.layer2_provider = provider
        config.layer2_local_model_path = local_model_path
        
        # 初始化向量数据库管理器
        vector_db_manager = VectorDatabaseManager()
        
        # 初始化增强的双层检测系统
        self.detection_system = TwoLayerDetectionSystem(
            config=config,
            vector_db_manager=vector_db_manager
        )
        
        print(f"✅ 增强双层检测系统初始化完成")
        print(f"   模型: {model}")
        print(f"   提供者: {provider}")
        print(f"   数据集: {dataset_name}")
    
    def detect(self, text):
        """
        检测文本是否为仇恨言论
        
        Args:
            text: 输入文本
            
        Returns:
            dict: 检测结果，包含verdict, explanation, processing_time等
        """
        start_time = time.time()
        
        try:
            # 使用增强的双层检测系统进行检测
            result = self.detection_system.detect(text, self.dataset_name)
            
            processing_time = time.time() - start_time
            
            # 构造返回结果，确保格式与其他检测器一致
            detection_result = {
                'verdict': result.verdict,
                'confidence': result.confidence,
                'processing_time': processing_time,
                'explanation': f"Decision path: {result.decision_path}",
                
                # 增强系统特有的信息
                'layer2_triggered': result.layer2_triggered,
                'is_ambiguous': result.is_ambiguous,
                'ambiguity_score': result.ambiguity_score,
                'ambiguity_reasons': result.ambiguity_reasons,
                'decision_path': result.decision_path,
                
                # 第一层结果
                'layer1_toxigen_result': result.layer1_toxigen_result,
                'layer1_hurtlex_result': result.layer1_hurtlex_result,
                'fusion_details': result.fusion_details,
                
                # 第二层结果（如果触发）
                'layer2_single_result': result.layer2_single_result if result.layer2_triggered else None,
                'layer2_retrieval_result': result.layer2_retrieval_result if result.layer2_triggered else None,
                
                # 系统类型标识
                'detector_type': 'enhanced_two_layer_system',
                'system_type': 'enhanced_multi_agent'
            }
            
            return detection_result
            
        except Exception as e:
            processing_time = time.time() - start_time
            print(f"检测过程出错: {e}")
            
            return {
                'verdict': 0,  # 默认为非仇恨言论
                'confidence': 0.0,
                'processing_time': processing_time,
                'explanation': f"Error occurred: {str(e)}",
                'error': str(e),
                'detector_type': 'enhanced_two_layer_system',
                'system_type': 'enhanced_multi_agent'
            }

def main():
    """运行增强系统评估"""
    parser = argparse.ArgumentParser(description="Run enhanced two-layer system evaluation")
    parser.add_argument("--dataset", type=str, default="ImplicitHate",
                        choices=["ImplicitHate", "HateSpeechOffensive", "HateSpeechStormfront"],
                        help="Name of the dataset to evaluate")
    parser.add_argument("--num_samples", type=int, default=50,
                        help="Number of samples to evaluate")
    parser.add_argument("--start_idx", type=int, default=0,
                        help="Starting index of samples")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo-0125",
                        help="Name of the model to use")
    parser.add_argument("--provider", type=str, choices=["api", "ollama", "local"], default="api",
                        help="Model provider")
    parser.add_argument("--ollama-base-url", type=str, default="http://localhost:11434",
                        help="Ollama API base URL")
    parser.add_argument("--local-model-path", type=str,
                        help="Path to local model (used when provider is 'local')")

    args = parser.parse_args()

    if args.provider == "local" and not args.local_model_path:
        parser.error("--local-model-path is required when using a local provider.")

    # 当使用本地模型时，自动从路径解析模型名称
    if args.provider == "local":
        args.model = os.path.basename(args.local_model_path.rstrip('/\\'))

    print("=" * 80)
    print("增强双层多智能体系统评估")
    print("=" * 80)
    print(f"数据集: {args.dataset}")
    print(f"样本数: {args.num_samples}, 起始索引: {args.start_idx}")
    print(f"模型提供者: {args.provider}, 模型: {args.model}")
    print()

    model_path = args.model
    if args.provider == "local":
        model_path = args.local_model_path
        print(f"使用本地模型: {model_path}")

    # 创建增强系统检测器
    detector = EnhancedSystemDetector(
        model=args.model,
        provider=args.provider,
        ollama_base_url=args.ollama_base_url,
        local_model_path=model_path,
        dataset_name=args.dataset
    )
    
    # 创建评估器
    evaluator = ModelEvaluator(
        detector=detector,
        dataset_name=args.dataset,
        num_samples=args.num_samples,
        start_idx=args.start_idx
    )
    
    # 运行评估
    print(f"\n开始评估增强双层多智能体系统...")
    start_time = time.time()
    
    evaluator.evaluate()
    
    evaluation_time = time.time() - start_time
    
    # 绘制指标图
    evaluator.plot_metrics()
    
    # 获取系统统计信息
    system_stats = detector.detection_system.stats
    ambiguity_stats = detector.detection_system.ambiguity_detector.get_detection_statistics()
    
    print(f"\n增强双层多智能体系统评估完成")
    print(f"总评估时间: {evaluation_time:.2f}秒")
    print(f"日志文件已保存到 logs/ 目录")
    print(f"结果图表已保存到 results/ 目录")
    
    # 打印系统统计信息
    print(f"\n系统统计信息:")
    print(f"  总处理样本: {system_stats.get('total_processed', 0)}")
    print(f"  第一层直接决策: {system_stats.get('layer1_only', 0)}")
    print(f"  第二层触发: {system_stats.get('layer2_triggered', 0)}")
    print(f"  第二层触发率: {system_stats.get('layer2_triggered', 0) / max(system_stats.get('total_processed', 1), 1) * 100:.1f}%")
    print(f"  平均处理时间: {system_stats.get('total_processing_time', 0) / max(system_stats.get('total_processed', 1), 1):.3f}秒")
    
    print(f"\n歧义检测统计:")
    print(f"  总检测次数: {ambiguity_stats.get('total_detections', 0)}")
    print(f"  置信度校准触发: {ambiguity_stats.get('calibration_triggered', 0)}")
    print(f"  熵检测触发: {ambiguity_stats.get('entropy_triggered', 0)}")
    print(f"  两者都触发: {ambiguity_stats.get('both_triggered', 0)}")
    print(f"  一致性率: {ambiguity_stats.get('consensus_rate', 0) * 100:.1f}%")
    
    # 保存详细统计信息
    detailed_stats = {
        'evaluation_info': {
            'dataset': args.dataset,
            'num_samples': args.num_samples,
            'start_idx': args.start_idx,
            'model': args.model,
            'provider': args.provider,
            'evaluation_time': evaluation_time,
            'timestamp': datetime.now().isoformat()
        },
        'system_stats': system_stats,
        'ambiguity_stats': ambiguity_stats
    }
    
    stats_filename = f"enhanced_system_stats_{args.dataset}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(detailed_stats, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n详细统计信息已保存到: {stats_filename}")
    except Exception as e:
        print(f"保存统计信息失败: {e}")

if __name__ == "__main__":
    main()
