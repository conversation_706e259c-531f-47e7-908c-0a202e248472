#!/usr/bin/env python3
"""
增强歧义检测集成测试
"""

import unittest
import sys
import os
import tempfile

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_modules.enhanced_ambiguity_detector import (
    EnhancedAmbiguityDetector,
    EnhancedAmbiguityConfig
)

class TestEnhancedIntegration(unittest.TestCase):
    """增强歧义检测集成测试"""
    
    def setUp(self):
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建配置
        config = EnhancedAmbiguityConfig()
        config.calibration_config.calibration_data_path = self.temp_dir
        config.entropy_config.analysis_data_path = self.temp_dir
        
        self.detector = EnhancedAmbiguityDetector(config)
    
    def tearDown(self):
        # 清理临时目录
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_basic_integration(self):
        """测试基本集成功能"""
        text = "This is a test message"
        model_results = {
            'toxigen': {'verdict': 0, 'confidence': 0.8},
            'hurtlex': {'verdict': 0, 'confidence': 0.75}
        }
        
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, model_results)
        
        self.assertIsInstance(is_ambiguous, bool)
        self.assertIsInstance(reasons, list)
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 1)
    
    def test_disagreement_detection(self):
        """测试分歧检测"""
        text = "This is a controversial message"
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.6},
            'hurtlex': {'verdict': 0, 'confidence': 0.7}
        }
        
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, model_results)
        
        # 分歧应该被检测为歧义
        self.assertTrue(is_ambiguous)
        self.assertGreater(len(reasons), 0)
        self.assertGreater(score, 0.2)
    
    def test_low_confidence_detection(self):
        """测试低置信度检测"""
        text = "This is an uncertain message"
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.4},
            'hurtlex': {'verdict': 1, 'confidence': 0.45}
        }
        
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, model_results)
        
        # 低置信度应该有较高的歧义分数（可能被检测为歧义）
        self.assertGreater(score, 0.2)  # 至少有较高的歧义分数
    
    def test_calibration_update(self):
        """测试校准更新"""
        toxigen_result = {'verdict': 1, 'confidence': 0.7}
        hurtlex_result = {'verdict': 0, 'confidence': 0.6}
        true_label = 1
        performance_metrics = {'accuracy': 1.0, 'f1': 0.8}
        
        # 更新校准应该不抛出异常
        try:
            self.detector.update_calibration(
                toxigen_result, hurtlex_result, true_label, performance_metrics
            )
            success = True
        except Exception as e:
            print(f"校准更新失败: {e}")
            success = False
        
        self.assertTrue(success)
    
    def test_statistics_collection(self):
        """测试统计信息收集"""
        # 运行几次检测
        test_cases = [
            ("Normal message", {'toxigen': {'verdict': 0, 'confidence': 0.9}, 'hurtlex': {'verdict': 0, 'confidence': 0.85}}),
            ("Controversial message", {'toxigen': {'verdict': 1, 'confidence': 0.6}, 'hurtlex': {'verdict': 0, 'confidence': 0.7}}),
            ("Uncertain message", {'toxigen': {'verdict': 1, 'confidence': 0.4}, 'hurtlex': {'verdict': 1, 'confidence': 0.45}})
        ]
        
        for text, model_results in test_cases:
            self.detector.detect_ambiguity(text, model_results)
        
        # 获取统计信息
        stats = self.detector.get_detection_statistics()
        
        self.assertIn('total_detections', stats)
        self.assertEqual(stats['total_detections'], 3)
        self.assertIn('consensus_rate', stats)
        self.assertGreaterEqual(stats['consensus_rate'], 0)
        self.assertLessEqual(stats['consensus_rate'], 1)
    
    def test_fusion_methods(self):
        """测试不同的融合方法"""
        text = "Test message"
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.6},
            'hurtlex': {'verdict': 0, 'confidence': 0.7}
        }
        
        fusion_methods = ["weighted_average", "max", "consensus"]
        
        for method in fusion_methods:
            # 创建新的检测器配置
            config = EnhancedAmbiguityConfig()
            config.fusion_method = method
            detector = EnhancedAmbiguityDetector(config)
            
            is_ambiguous, reasons, score = detector.detect_ambiguity(text, model_results)
            
            self.assertIsInstance(is_ambiguous, bool)
            self.assertIsInstance(score, float)
            self.assertGreaterEqual(score, 0)
            self.assertLessEqual(score, 1)
    
    def test_single_method_fallback(self):
        """测试单方法回退"""
        # 创建只启用一种方法的配置
        config = EnhancedAmbiguityConfig()
        config.use_confidence_calibration = True
        config.use_entropy_detection = False
        
        detector = EnhancedAmbiguityDetector(config)
        
        text = "Test message"
        model_results = {
            'toxigen': {'verdict': 1, 'confidence': 0.6},
            'hurtlex': {'verdict': 0, 'confidence': 0.7}
        }
        
        is_ambiguous, reasons, score = detector.detect_ambiguity(text, model_results)
        
        self.assertIsInstance(is_ambiguous, bool)
        self.assertIsInstance(score, float)
    
    def test_error_handling(self):
        """测试错误处理"""
        text = "Test message"
        
        # 测试空的模型结果
        empty_results = {}
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, empty_results)
        
        self.assertIsInstance(is_ambiguous, bool)
        self.assertIsInstance(reasons, list)
        self.assertIsInstance(score, float)
        
        # 测试不完整的模型结果
        incomplete_results = {
            'toxigen': {'verdict': 1}  # 缺少confidence
        }
        is_ambiguous, reasons, score = self.detector.detect_ambiguity(text, incomplete_results)
        
        self.assertIsInstance(is_ambiguous, bool)
        self.assertIsInstance(reasons, list)
        self.assertIsInstance(score, float)

def run_integration_test():
    """运行集成测试"""
    print("运行增强歧义检测集成测试...")
    
    # 创建检测器
    detector = EnhancedAmbiguityDetector()
    
    # 测试用例
    test_cases = [
        {
            'name': '一致预测（低歧义）',
            'text': 'This is a normal message',
            'model_results': {
                'toxigen': {'verdict': 0, 'confidence': 0.9},
                'hurtlex': {'verdict': 0, 'confidence': 0.85}
            }
        },
        {
            'name': '分歧预测（高歧义）',
            'text': 'This is a controversial message',
            'model_results': {
                'toxigen': {'verdict': 1, 'confidence': 0.6},
                'hurtlex': {'verdict': 0, 'confidence': 0.7}
            }
        },
        {
            'name': '低置信度（中等歧义）',
            'text': 'This is an uncertain message',
            'model_results': {
                'toxigen': {'verdict': 1, 'confidence': 0.4},
                'hurtlex': {'verdict': 1, 'confidence': 0.45}
            }
        }
    ]
    
    print("\n测试结果:")
    print("-" * 80)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}:")
        print(f"   文本: {case['text']}")
        print(f"   模型结果: {case['model_results']}")
        
        is_ambiguous, reasons, score = detector.detect_ambiguity(
            case['text'], case['model_results']
        )
        
        print(f"   是否歧义: {is_ambiguous}")
        print(f"   歧义分数: {score:.4f}")
        print(f"   歧义原因: {reasons[:3]}...")  # 只显示前3个原因
    
    # 获取统计信息
    stats = detector.get_detection_statistics()
    print(f"\n统计信息:")
    print(f"   总检测次数: {stats['total_detections']}")
    print(f"   置信度校准触发: {stats['calibration_triggered']}")
    print(f"   熵检测触发: {stats['entropy_triggered']}")
    print(f"   两者都触发: {stats['both_triggered']}")
    print(f"   一致性率: {stats['consensus_rate']:.2%}")
    
    print("\n✅ 集成测试完成！")

if __name__ == '__main__':
    # 运行快速集成测试
    run_integration_test()
    
    # 运行完整单元测试
    print("\n" + "="*50)
    print("运行完整单元测试...")
    unittest.main(verbosity=2)
