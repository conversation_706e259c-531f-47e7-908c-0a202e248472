#!/usr/bin/env python3
"""
性能优化器
Performance Optimizer

提供缓存、并行处理、容错等性能优化功能
包括多级缓存、智能预取、负载均衡等

作者: AI Assistant
日期: 2025-08-02
版本: 1.0
"""

import time
import hashlib
import pickle
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed, Future
import logging
from pathlib import Path
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CacheConfig:
    """缓存配置"""
    # 内存缓存配置
    memory_cache_size: int = 1000
    memory_cache_ttl: float = 3600.0  # 1小时
    
    # 磁盘缓存配置
    enable_disk_cache: bool = True
    disk_cache_dir: str = "cache"
    disk_cache_size: int = 10000
    disk_cache_ttl: float = 86400.0  # 24小时
    
    # 缓存策略
    cache_strategy: str = "lru"  # lru, lfu, fifo
    enable_compression: bool = True

@dataclass
class ParallelConfig:
    """并行处理配置"""
    max_workers: int = 4
    timeout: float = 30.0
    enable_load_balancing: bool = True
    batch_size: int = 10

@dataclass
class FaultToleranceConfig:
    """容错配置"""
    max_retries: int = 3
    retry_delay: float = 1.0
    enable_circuit_breaker: bool = True
    failure_threshold: int = 5
    recovery_timeout: float = 60.0

class MultiLevelCache:
    """多级缓存系统"""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        
        # 内存缓存 (L1)
        self.memory_cache = {}
        self.memory_access_times = {}
        self.memory_access_counts = {}
        
        # 磁盘缓存 (L2)
        if self.config.enable_disk_cache:
            self.disk_cache_dir = Path(self.config.disk_cache_dir)
            self.disk_cache_dir.mkdir(exist_ok=True)
            self.disk_cache_index = self._load_disk_cache_index()
        
        # 缓存统计
        self.stats = {
            'memory_hits': 0,
            'memory_misses': 0,
            'disk_hits': 0,
            'disk_misses': 0,
            'total_requests': 0
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        logger.info("多级缓存系统初始化完成")
    
    def _generate_cache_key(self, data: Any) -> str:
        """生成缓存键"""
        if isinstance(data, str):
            content = data
        else:
            content = str(data)
        
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _load_disk_cache_index(self) -> Dict:
        """加载磁盘缓存索引"""
        index_file = self.disk_cache_dir / "cache_index.json"
        if index_file.exists():
            try:
                with open(index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载磁盘缓存索引失败: {e}")
        return {}
    
    def _save_disk_cache_index(self):
        """保存磁盘缓存索引"""
        if not self.config.enable_disk_cache:
            return
        
        index_file = self.disk_cache_dir / "cache_index.json"
        try:
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(self.disk_cache_index, f, indent=2)
        except Exception as e:
            logger.warning(f"保存磁盘缓存索引失败: {e}")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        with self.lock:
            self.stats['total_requests'] += 1
            
            # 检查内存缓存 (L1)
            if key in self.memory_cache:
                access_time = self.memory_access_times.get(key, 0)
                if time.time() - access_time < self.config.memory_cache_ttl:
                    self.memory_access_times[key] = time.time()
                    self.memory_access_counts[key] = self.memory_access_counts.get(key, 0) + 1
                    self.stats['memory_hits'] += 1
                    return self.memory_cache[key]
                else:
                    # 过期，删除
                    del self.memory_cache[key]
                    del self.memory_access_times[key]
                    if key in self.memory_access_counts:
                        del self.memory_access_counts[key]
            
            self.stats['memory_misses'] += 1
            
            # 检查磁盘缓存 (L2)
            if self.config.enable_disk_cache and key in self.disk_cache_index:
                cache_info = self.disk_cache_index[key]
                if time.time() - cache_info['timestamp'] < self.config.disk_cache_ttl:
                    try:
                        cache_file = self.disk_cache_dir / f"{key}.cache"
                        if cache_file.exists():
                            with open(cache_file, 'rb') as f:
                                data = pickle.load(f)
                            
                            # 提升到内存缓存
                            self._put_memory_cache(key, data)
                            self.stats['disk_hits'] += 1
                            return data
                    except Exception as e:
                        logger.warning(f"读取磁盘缓存失败: {e}")
                        # 删除损坏的缓存
                        if key in self.disk_cache_index:
                            del self.disk_cache_index[key]
            
            self.stats['disk_misses'] += 1
            return None
    
    def put(self, key: str, value: Any):
        """存储缓存数据"""
        with self.lock:
            # 存储到内存缓存
            self._put_memory_cache(key, value)
            
            # 存储到磁盘缓存
            if self.config.enable_disk_cache:
                self._put_disk_cache(key, value)
    
    def _put_memory_cache(self, key: str, value: Any):
        """存储到内存缓存"""
        # 检查缓存大小限制
        if len(self.memory_cache) >= self.config.memory_cache_size:
            self._evict_memory_cache()
        
        self.memory_cache[key] = value
        self.memory_access_times[key] = time.time()
        self.memory_access_counts[key] = 1
    
    def _put_disk_cache(self, key: str, value: Any):
        """存储到磁盘缓存"""
        try:
            # 检查缓存大小限制
            if len(self.disk_cache_index) >= self.config.disk_cache_size:
                self._evict_disk_cache()
            
            cache_file = self.disk_cache_dir / f"{key}.cache"
            with open(cache_file, 'wb') as f:
                pickle.dump(value, f)
            
            self.disk_cache_index[key] = {
                'timestamp': time.time(),
                'size': cache_file.stat().st_size
            }
            
            # 定期保存索引
            if len(self.disk_cache_index) % 100 == 0:
                self._save_disk_cache_index()
                
        except Exception as e:
            logger.warning(f"存储磁盘缓存失败: {e}")
    
    def _evict_memory_cache(self):
        """内存缓存淘汰"""
        if self.config.cache_strategy == "lru":
            # 最近最少使用
            oldest_key = min(self.memory_access_times.keys(), 
                           key=lambda k: self.memory_access_times[k])
        elif self.config.cache_strategy == "lfu":
            # 最少使用频率
            oldest_key = min(self.memory_access_counts.keys(),
                           key=lambda k: self.memory_access_counts[k])
        else:  # fifo
            # 先进先出
            oldest_key = next(iter(self.memory_cache))
        
        del self.memory_cache[oldest_key]
        del self.memory_access_times[oldest_key]
        if oldest_key in self.memory_access_counts:
            del self.memory_access_counts[oldest_key]
    
    def _evict_disk_cache(self):
        """磁盘缓存淘汰"""
        # 删除最旧的缓存文件
        oldest_key = min(self.disk_cache_index.keys(),
                        key=lambda k: self.disk_cache_index[k]['timestamp'])
        
        cache_file = self.disk_cache_dir / f"{oldest_key}.cache"
        if cache_file.exists():
            cache_file.unlink()
        
        del self.disk_cache_index[oldest_key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_hits = self.stats['memory_hits'] + self.stats['disk_hits']
            total_misses = self.stats['memory_misses'] + self.stats['disk_misses']
            total_requests = self.stats['total_requests']
            
            return {
                **self.stats,
                'hit_rate': total_hits / total_requests if total_requests > 0 else 0,
                'memory_hit_rate': self.stats['memory_hits'] / total_requests if total_requests > 0 else 0,
                'disk_hit_rate': self.stats['disk_hits'] / total_requests if total_requests > 0 else 0,
                'memory_cache_size': len(self.memory_cache),
                'disk_cache_size': len(self.disk_cache_index) if self.config.enable_disk_cache else 0
            }
    
    def clear(self):
        """清除所有缓存"""
        with self.lock:
            self.memory_cache.clear()
            self.memory_access_times.clear()
            self.memory_access_counts.clear()
            
            if self.config.enable_disk_cache:
                # 删除磁盘缓存文件
                for cache_file in self.disk_cache_dir.glob("*.cache"):
                    cache_file.unlink()
                self.disk_cache_index.clear()
                self._save_disk_cache_index()
            
            # 重置统计
            for key in self.stats:
                self.stats[key] = 0
            
            logger.info("缓存已清除")

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, config: FaultToleranceConfig):
        self.config = config
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """通过熔断器调用函数"""
        with self.lock:
            if self.state == "OPEN":
                if time.time() - self.last_failure_time > self.config.recovery_timeout:
                    self.state = "HALF_OPEN"
                    logger.info("熔断器进入半开状态")
                else:
                    raise Exception("熔断器开启，拒绝请求")
        
        try:
            result = func(*args, **kwargs)
            
            with self.lock:
                if self.state == "HALF_OPEN":
                    self.state = "CLOSED"
                    self.failure_count = 0
                    logger.info("熔断器恢复正常")
            
            return result
            
        except Exception as e:
            with self.lock:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.config.failure_threshold:
                    self.state = "OPEN"
                    logger.warning(f"熔断器开启，失败次数: {self.failure_count}")
            
            raise e

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, 
                 cache_config: CacheConfig = None,
                 parallel_config: ParallelConfig = None,
                 fault_tolerance_config: FaultToleranceConfig = None):
        """
        初始化性能优化器
        
        Args:
            cache_config: 缓存配置
            parallel_config: 并行处理配置
            fault_tolerance_config: 容错配置
        """
        self.cache_config = cache_config or CacheConfig()
        self.parallel_config = parallel_config or ParallelConfig()
        self.fault_tolerance_config = fault_tolerance_config or FaultToleranceConfig()
        
        # 初始化组件
        self.cache = MultiLevelCache(self.cache_config)
        self.circuit_breaker = CircuitBreaker(self.fault_tolerance_config)
        self.executor = ThreadPoolExecutor(max_workers=self.parallel_config.max_workers)
        
        logger.info("性能优化器初始化完成")
    
    def cached_call(self, func: Callable, cache_key: str, *args, **kwargs):
        """带缓存的函数调用"""
        # 检查缓存
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 存储到缓存
        self.cache.put(cache_key, result)
        
        return result
    
    def fault_tolerant_call(self, func: Callable, *args, **kwargs):
        """容错函数调用"""
        for attempt in range(self.fault_tolerance_config.max_retries + 1):
            try:
                if self.fault_tolerance_config.enable_circuit_breaker:
                    return self.circuit_breaker.call(func, *args, **kwargs)
                else:
                    return func(*args, **kwargs)
                    
            except Exception as e:
                if attempt < self.fault_tolerance_config.max_retries:
                    logger.warning(f"函数调用失败，重试 {attempt + 1}/{self.fault_tolerance_config.max_retries}: {e}")
                    time.sleep(self.fault_tolerance_config.retry_delay * (2 ** attempt))  # 指数退避
                else:
                    logger.error(f"函数调用最终失败: {e}")
                    raise e
    
    def parallel_process(self, func: Callable, items: List[Any], **kwargs) -> List[Any]:
        """并行处理"""
        if len(items) <= 1:
            return [func(item, **kwargs) for item in items]
        
        results = [None] * len(items)
        futures = {}
        
        # 提交任务
        for i, item in enumerate(items):
            future = self.executor.submit(func, item, **kwargs)
            futures[future] = i
        
        # 收集结果
        for future in as_completed(futures, timeout=self.parallel_config.timeout):
            index = futures[future]
            try:
                results[index] = future.result()
            except Exception as e:
                logger.error(f"并行任务 {index} 失败: {e}")
                results[index] = None
        
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        cache_stats = self.cache.get_stats()
        
        return {
            'cache_stats': cache_stats,
            'circuit_breaker_state': self.circuit_breaker.state,
            'circuit_breaker_failures': self.circuit_breaker.failure_count,
            'thread_pool_active': self.executor._threads,
            'thread_pool_max_workers': self.parallel_config.max_workers
        }
    
    def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=True)
        self.cache.clear()
        logger.info("性能优化器资源已清理")
