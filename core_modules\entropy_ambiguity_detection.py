#!/usr/bin/env python3
"""
基于熵的歧义检测增强模块
Entropy-based Ambiguity Detection Enhancement Module

实现预测熵计算、KL散度和贝叶斯不确定性估计功能
基于Malinin & Gales (2018) "Predictive Uncertainty Estimation via Prior Networks"

作者: AI Assistant
日期: 2025-08-04
版本: 1.0
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from dataclasses import dataclass
import json
import os
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class EntropyDetectionConfig:
    """基于熵的歧义检测配置"""
    # 熵阈值参数
    entropy_threshold: float = 1.0
    ensemble_entropy_threshold: float = 0.8
    disagreement_threshold: float = 0.5
    
    # 贝叶斯不确定性参数
    mc_dropout_samples: int = 100
    dropout_rate: float = 0.1
    epistemic_threshold: float = 0.3
    aleatoric_threshold: float = 0.6
    
    # 权重组合参数
    entropy_weight: float = 0.3
    disagreement_weight: float = 0.25
    epistemic_weight: float = 0.25
    aleatoric_weight: float = 0.2
    
    # 歧义原因分析阈值
    high_entropy_threshold: float = 0.8
    high_disagreement_threshold: float = 0.4
    asymmetric_confidence_threshold: float = 0.6
    
    # 历史记录
    history_size: int = 1000
    save_analysis_data: bool = True
    analysis_data_path: str = "./entropy_analysis_data"

class EntropyCalculator:
    """熵计算器"""
    
    def __init__(self, config: EntropyDetectionConfig = None):
        self.config = config or EntropyDetectionConfig()
    
    def calculate_shannon_entropy(self, probabilities: np.ndarray, base: float = 2.0) -> float:
        """
        计算Shannon熵
        
        Args:
            probabilities: 概率分布
            base: 对数底数 (2 for bits, e for nats)
            
        Returns:
            熵值
        """
        # 避免log(0)
        probabilities = np.clip(probabilities, 1e-10, 1.0)
        
        if base == 2.0:
            entropy = -np.sum(probabilities * np.log2(probabilities))
        elif base == np.e:
            entropy = -np.sum(probabilities * np.log(probabilities))
        else:
            entropy = -np.sum(probabilities * np.log(probabilities) / np.log(base))
        
        return entropy
    
    def calculate_conditional_entropy(self, joint_probs: np.ndarray) -> float:
        """计算条件熵 H(Y|X)"""
        # 简化实现，假设joint_probs是联合概率分布
        marginal_x = np.sum(joint_probs, axis=1)
        conditional_entropy = 0.0
        
        for i in range(joint_probs.shape[0]):
            if marginal_x[i] > 1e-10:
                conditional_probs = joint_probs[i, :] / marginal_x[i]
                conditional_entropy += marginal_x[i] * self.calculate_shannon_entropy(conditional_probs)
        
        return conditional_entropy
    
    def calculate_mutual_information(self, pred1: np.ndarray, pred2: np.ndarray) -> float:
        """
        计算互信息 I(X;Y) = H(X) + H(Y) - H(X,Y)
        
        Args:
            pred1: 第一个模型的预测概率
            pred2: 第二个模型的预测概率
            
        Returns:
            互信息值
        """
        h_x = self.calculate_shannon_entropy(pred1)
        h_y = self.calculate_shannon_entropy(pred2)
        
        # 简化的联合熵计算（假设独立性）
        joint_entropy = h_x + h_y  # 这是一个简化，实际应该计算真正的联合熵
        
        # 更准确的互信息计算需要真正的联合分布
        # 这里使用KL散度作为近似
        kl_div = self.calculate_kl_divergence(pred1, pred2)
        mutual_info = max(0.0, h_x + h_y - joint_entropy - kl_div)

        return float(mutual_info)
    
    def calculate_kl_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """计算KL散度 D_KL(P||Q)"""
        p = np.clip(p, 1e-10, 1.0)
        q = np.clip(q, 1e-10, 1.0)
        return np.sum(p * np.log2(p / q))
    
    def calculate_js_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """计算Jensen-Shannon散度"""
        m = 0.5 * (p + q)
        js_div = 0.5 * self.calculate_kl_divergence(p, m) + 0.5 * self.calculate_kl_divergence(q, m)
        return js_div
    
    def calculate_ensemble_entropy(self, predictions_list: List[np.ndarray]) -> float:
        """计算集成预测的熵"""
        mean_prediction = np.mean(predictions_list, axis=0)
        return self.calculate_shannon_entropy(mean_prediction)
    
    def calculate_predictive_entropy(self, predictions_list: List[np.ndarray]) -> float:
        """计算预测熵（平均单个预测的熵）"""
        entropies = [self.calculate_shannon_entropy(pred) for pred in predictions_list]
        return np.mean(entropies)

class ModelDisagreementMeasure:
    """模型分歧度量器"""
    
    def __init__(self, config: EntropyDetectionConfig = None):
        self.config = config or EntropyDetectionConfig()
        self.entropy_calculator = EntropyCalculator(config)
    
    def measure_prediction_disagreement(self, pred1: np.ndarray, pred2: np.ndarray) -> Dict[str, float]:
        """
        测量预测分歧
        
        Args:
            pred1: 第一个模型预测
            pred2: 第二个模型预测
            
        Returns:
            分歧度量字典
        """
        disagreement_metrics = {}
        
        # 1. KL散度（非对称）
        disagreement_metrics['kl_p_to_q'] = self.entropy_calculator.calculate_kl_divergence(pred1, pred2)
        disagreement_metrics['kl_q_to_p'] = self.entropy_calculator.calculate_kl_divergence(pred2, pred1)
        
        # 2. 对称KL散度
        disagreement_metrics['symmetric_kl'] = (
            disagreement_metrics['kl_p_to_q'] + disagreement_metrics['kl_q_to_p']
        ) / 2
        
        # 3. Jensen-Shannon散度
        disagreement_metrics['js_divergence'] = self.entropy_calculator.calculate_js_divergence(pred1, pred2)
        
        # 4. L2距离
        disagreement_metrics['l2_distance'] = np.linalg.norm(pred1 - pred2)
        
        # 5. L1距离（总变差距离）
        disagreement_metrics['l1_distance'] = np.sum(np.abs(pred1 - pred2)) / 2
        
        # 6. 余弦相似度
        dot_product = np.dot(pred1, pred2)
        norm_product = np.linalg.norm(pred1) * np.linalg.norm(pred2)
        cosine_similarity = dot_product / (norm_product + 1e-10)
        disagreement_metrics['cosine_disagreement'] = 1 - cosine_similarity
        
        # 7. 预测类别分歧
        pred1_class = np.argmax(pred1)
        pred2_class = np.argmax(pred2)
        disagreement_metrics['class_disagreement'] = float(pred1_class != pred2_class)
        
        # 8. 置信度差异
        conf1 = np.max(pred1)
        conf2 = np.max(pred2)
        disagreement_metrics['confidence_gap'] = abs(conf1 - conf2)
        
        return disagreement_metrics
    
    def analyze_disagreement_patterns(self, disagreement_history: List[Dict[str, float]]) -> Dict[str, Any]:
        """分析分歧模式"""
        if not disagreement_history:
            return {}
        
        patterns = {}
        
        # 计算各种分歧度量的统计信息
        for metric in disagreement_history[0].keys():
            values = [d[metric] for d in disagreement_history]
            patterns[f'{metric}_mean'] = np.mean(values)
            patterns[f'{metric}_std'] = np.std(values)
            patterns[f'{metric}_max'] = np.max(values)
            patterns[f'{metric}_min'] = np.min(values)
        
        # 分析趋势
        if len(disagreement_history) >= 10:
            recent_symmetric_kl = [d['symmetric_kl'] for d in disagreement_history[-10:]]
            early_symmetric_kl = [d['symmetric_kl'] for d in disagreement_history[:10]]
            
            patterns['disagreement_trend'] = np.mean(recent_symmetric_kl) - np.mean(early_symmetric_kl)
        
        return patterns

class BayesianUncertaintyEstimator:
    """贝叶斯不确定性估计器"""
    
    def __init__(self, config: EntropyDetectionConfig = None):
        self.config = config or EntropyDetectionConfig()
        self.entropy_calculator = EntropyCalculator(config)
    
    def estimate_mc_dropout_uncertainty(self, 
                                      model_predictions: List[np.ndarray]) -> Dict[str, float]:
        """
        使用Monte Carlo Dropout估计不确定性
        
        Args:
            model_predictions: 多次dropout采样的预测结果列表
            
        Returns:
            不确定性估计字典
        """
        if len(model_predictions) < 2:
            return {
                'epistemic_uncertainty': 0.0,
                'aleatoric_uncertainty': 0.0,
                'total_uncertainty': 0.0,
                'prediction_variance': 0.0
            }
        
        predictions_array = np.array(model_predictions)
        
        # 1. 认知不确定性（模型不确定性）
        # 使用预测的方差作为认知不确定性的代理
        prediction_variance = np.var(predictions_array, axis=0)
        epistemic_uncertainty = np.mean(prediction_variance)
        
        # 2. 偶然不确定性（数据不确定性）
        # 使用平均预测熵
        individual_entropies = [self.entropy_calculator.calculate_shannon_entropy(pred) 
                              for pred in model_predictions]
        aleatoric_uncertainty = np.mean(individual_entropies)
        
        # 3. 总不确定性
        mean_prediction = np.mean(predictions_array, axis=0)
        total_uncertainty = self.entropy_calculator.calculate_shannon_entropy(mean_prediction)
        
        # 4. 互信息（认知不确定性的另一种度量）
        mutual_information = total_uncertainty - aleatoric_uncertainty
        
        return {
            'epistemic_uncertainty': epistemic_uncertainty,
            'aleatoric_uncertainty': aleatoric_uncertainty,
            'total_uncertainty': total_uncertainty,
            'mutual_information': max(0, mutual_information),
            'prediction_variance': np.mean(prediction_variance),
            'prediction_std': np.sqrt(epistemic_uncertainty)
        }
    
    def estimate_ensemble_uncertainty(self, 
                                    model_predictions: List[np.ndarray]) -> Dict[str, float]:
        """
        估计集成模型的不确定性
        
        Args:
            model_predictions: 不同模型的预测结果
            
        Returns:
            集成不确定性估计
        """
        if len(model_predictions) < 2:
            return {'ensemble_uncertainty': 0.0}
        
        # 集成预测
        ensemble_prediction = np.mean(model_predictions, axis=0)
        
        # 集成不确定性度量
        uncertainty_metrics = {}
        
        # 1. 集成熵
        uncertainty_metrics['ensemble_entropy'] = self.entropy_calculator.calculate_shannon_entropy(
            ensemble_prediction
        )
        
        # 2. 预测分歧
        pairwise_disagreements = []
        for i in range(len(model_predictions)):
            for j in range(i + 1, len(model_predictions)):
                disagreement = self.entropy_calculator.calculate_kl_divergence(
                    model_predictions[i], model_predictions[j]
                )
                pairwise_disagreements.append(disagreement)
        
        uncertainty_metrics['mean_pairwise_disagreement'] = np.mean(pairwise_disagreements)
        uncertainty_metrics['max_pairwise_disagreement'] = np.max(pairwise_disagreements)
        
        # 3. 预测方差
        predictions_array = np.array(model_predictions)
        uncertainty_metrics['prediction_variance'] = np.mean(np.var(predictions_array, axis=0))
        
        # 4. 一致性度量
        predicted_classes = [np.argmax(pred) for pred in model_predictions]
        class_counts = np.bincount(predicted_classes, minlength=len(ensemble_prediction))
        uncertainty_metrics['class_consensus'] = np.max(class_counts) / len(model_predictions)
        uncertainty_metrics['class_entropy'] = self.entropy_calculator.calculate_shannon_entropy(
            class_counts / len(model_predictions)
        )
        
        return uncertainty_metrics
    
    def decompose_uncertainty(self, 
                            model_predictions: List[np.ndarray],
                            method: str = 'mutual_information') -> Dict[str, float]:
        """
        分解不确定性为认知和偶然成分
        
        Args:
            model_predictions: 模型预测列表
            method: 分解方法 ('mutual_information', 'variance_decomposition')
            
        Returns:
            不确定性分解结果
        """
        if method == 'mutual_information':
            return self.estimate_mc_dropout_uncertainty(model_predictions)
        elif method == 'variance_decomposition':
            return self._variance_decomposition(model_predictions)
        else:
            raise ValueError(f"Unknown uncertainty decomposition method: {method}")
    
    def _variance_decomposition(self, model_predictions: List[np.ndarray]) -> Dict[str, float]:
        """基于方差的不确定性分解"""
        predictions_array = np.array(model_predictions)
        
        # 总方差
        total_variance = np.var(predictions_array.flatten())
        
        # 类内方差（偶然不确定性）
        within_class_variance = np.mean([np.var(pred) for pred in model_predictions])
        
        # 类间方差（认知不确定性）
        between_class_variance = total_variance - within_class_variance
        
        return {
            'total_variance': total_variance,
            'epistemic_variance': max(0, between_class_variance),
            'aleatoric_variance': within_class_variance,
            'epistemic_uncertainty': np.sqrt(max(0, between_class_variance)),
            'aleatoric_uncertainty': np.sqrt(within_class_variance)
        }

class EntropyBasedAmbiguityDetector:
    """基于熵的歧义检测器主类"""

    def __init__(self, config: EntropyDetectionConfig = None):
        self.config = config or EntropyDetectionConfig()

        # 初始化子模块
        self.entropy_calculator = EntropyCalculator(self.config)
        self.disagreement_measure = ModelDisagreementMeasure(self.config)
        self.uncertainty_estimator = BayesianUncertaintyEstimator(self.config)

        # 历史记录
        self.analysis_history = []
        self.disagreement_history = []

        # 确保分析数据目录存在
        if self.config.save_analysis_data:
            os.makedirs(self.config.analysis_data_path, exist_ok=True)

        logger.info("基于熵的歧义检测器初始化完成")

    def detect_ambiguity(self,
                        text: str,
                        model_results: Dict[str, Dict[str, Any]]) -> Tuple[bool, List[str], float]:
        """
        检测文本的歧义性

        Args:
            text: 输入文本
            model_results: 模型结果字典，格式为 {'model_name': {'verdict': int, 'confidence': float, ...}}

        Returns:
            (是否存在歧义, 歧义原因列表, 歧义分数)
        """
        # 提取模型预测
        model_predictions = self._extract_predictions(model_results)

        if len(model_predictions) < 2:
            logger.warning("需要至少两个模型的预测结果进行歧义检测")
            return False, [], 0.0

        # 计算各种不确定性指标
        uncertainty_analysis = self._comprehensive_uncertainty_analysis(model_predictions, model_results)

        # 分析歧义原因
        ambiguity_reasons = self._analyze_ambiguity_reasons(uncertainty_analysis, model_results)

        # 计算综合歧义分数
        ambiguity_score = self._calculate_comprehensive_ambiguity_score(uncertainty_analysis)

        # 判断是否存在歧义
        is_ambiguous = self._make_ambiguity_decision(uncertainty_analysis, ambiguity_score)

        # 记录分析结果
        analysis_result = {
            'text': text,
            'is_ambiguous': is_ambiguous,
            'ambiguity_score': ambiguity_score,
            'ambiguity_reasons': ambiguity_reasons,
            'uncertainty_analysis': uncertainty_analysis,
            'model_results': model_results
        }

        self._update_analysis_history(analysis_result)

        return is_ambiguous, ambiguity_reasons, ambiguity_score

    def _extract_predictions(self, model_results: Dict[str, Dict[str, Any]]) -> List[np.ndarray]:
        """从模型结果中提取预测概率"""
        predictions = []

        for model_name, result in model_results.items():
            # 尝试从不同字段提取概率
            if 'probabilities' in result:
                prob = np.array(result['probabilities'])
            elif 'confidence' in result and 'verdict' in result:
                # 从置信度和判决构造概率分布
                confidence = result['confidence']
                verdict = result['verdict']
                if verdict == 1:
                    prob = np.array([1 - confidence, confidence])
                else:
                    prob = np.array([confidence, 1 - confidence])
            else:
                # 默认均匀分布
                prob = np.array([0.5, 0.5])

            # 确保概率和为1
            prob = prob / np.sum(prob)
            predictions.append(prob)

        return predictions

    def _comprehensive_uncertainty_analysis(self,
                                          model_predictions: List[np.ndarray],
                                          model_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """综合不确定性分析"""
        analysis = {}

        # 1. 基本熵计算
        analysis['individual_entropies'] = {}
        for i, (model_name, pred) in enumerate(zip(model_results.keys(), model_predictions)):
            analysis['individual_entropies'][model_name] = self.entropy_calculator.calculate_shannon_entropy(pred)

        analysis['ensemble_entropy'] = self.entropy_calculator.calculate_ensemble_entropy(model_predictions)
        analysis['predictive_entropy'] = self.entropy_calculator.calculate_predictive_entropy(model_predictions)

        # 2. 模型分歧分析
        if len(model_predictions) >= 2:
            # 取前两个模型进行分歧分析
            disagreement_metrics = self.disagreement_measure.measure_prediction_disagreement(
                model_predictions[0], model_predictions[1]
            )
            analysis['disagreement_metrics'] = disagreement_metrics
            self.disagreement_history.append(disagreement_metrics)

        # 3. 贝叶斯不确定性估计
        uncertainty_estimates = self.uncertainty_estimator.estimate_ensemble_uncertainty(model_predictions)
        analysis['uncertainty_estimates'] = uncertainty_estimates

        # 4. 置信度分析
        confidences = []
        verdicts = []
        for result in model_results.values():
            confidences.append(result.get('confidence', 0.5))
            verdicts.append(result.get('verdict', 0))

        analysis['confidence_stats'] = {
            'mean_confidence': np.mean(confidences),
            'std_confidence': np.std(confidences),
            'min_confidence': np.min(confidences),
            'max_confidence': np.max(confidences),
            'confidence_range': np.max(confidences) - np.min(confidences)
        }

        analysis['verdict_stats'] = {
            'verdict_agreement': len(set(verdicts)) == 1,
            'verdict_distribution': dict(zip(*np.unique(verdicts, return_counts=True))),
            'majority_verdict': max(set(verdicts), key=verdicts.count) if verdicts else 0
        }

        return analysis

    def _analyze_ambiguity_reasons(self,
                                 uncertainty_analysis: Dict[str, Any],
                                 model_results: Dict[str, Dict[str, Any]]) -> List[str]:
        """分析歧义产生的原因"""
        reasons = []

        # 1. 高熵检测
        ensemble_entropy = uncertainty_analysis.get('ensemble_entropy', 0)
        if ensemble_entropy > self.config.high_entropy_threshold:
            reasons.append(f'high_ensemble_entropy_{ensemble_entropy:.3f}')

        predictive_entropy = uncertainty_analysis.get('predictive_entropy', 0)
        if predictive_entropy > self.config.high_entropy_threshold:
            reasons.append(f'high_predictive_entropy_{predictive_entropy:.3f}')

        # 2. 模型分歧检测
        disagreement_metrics = uncertainty_analysis.get('disagreement_metrics', {})
        symmetric_kl = disagreement_metrics.get('symmetric_kl', 0)
        if symmetric_kl > self.config.high_disagreement_threshold:
            reasons.append(f'high_model_disagreement_{symmetric_kl:.3f}')

        js_divergence = disagreement_metrics.get('js_divergence', 0)
        if js_divergence > self.config.high_disagreement_threshold:
            reasons.append(f'high_js_divergence_{js_divergence:.3f}')

        # 3. 置信度问题
        confidence_stats = uncertainty_analysis.get('confidence_stats', {})
        mean_confidence = confidence_stats.get('mean_confidence', 1.0)
        if mean_confidence < 0.6:
            reasons.append(f'low_mean_confidence_{mean_confidence:.3f}')

        confidence_range = confidence_stats.get('confidence_range', 0)
        if confidence_range > 0.3:
            reasons.append(f'high_confidence_variance_{confidence_range:.3f}')

        # 4. 判决分歧
        verdict_stats = uncertainty_analysis.get('verdict_stats', {})
        if not verdict_stats.get('verdict_agreement', True):
            reasons.append('verdict_disagreement')

        # 5. 不对称置信度模式
        individual_entropies = uncertainty_analysis.get('individual_entropies', {})
        if len(individual_entropies) >= 2:
            entropies = list(individual_entropies.values())
            if max(entropies) - min(entropies) > self.config.asymmetric_confidence_threshold:
                reasons.append(f'asymmetric_model_confidence_{max(entropies) - min(entropies):.3f}')

        # 6. 高不确定性
        uncertainty_estimates = uncertainty_analysis.get('uncertainty_estimates', {})
        prediction_variance = uncertainty_estimates.get('prediction_variance', 0)
        if prediction_variance > 0.1:
            reasons.append(f'high_prediction_variance_{prediction_variance:.3f}')

        return reasons

    def _calculate_comprehensive_ambiguity_score(self, uncertainty_analysis: Dict[str, Any]) -> float:
        """计算综合歧义分数"""
        score_components = {}

        # 1. 熵分数
        ensemble_entropy = uncertainty_analysis.get('ensemble_entropy', 0)
        score_components['entropy'] = min(ensemble_entropy / 2.0, 1.0)  # 归一化到[0,1]

        # 2. 分歧分数
        disagreement_metrics = uncertainty_analysis.get('disagreement_metrics', {})
        symmetric_kl = disagreement_metrics.get('symmetric_kl', 0)
        score_components['disagreement'] = min(symmetric_kl / 2.0, 1.0)

        # 3. 认知不确定性分数
        uncertainty_estimates = uncertainty_analysis.get('uncertainty_estimates', {})
        prediction_variance = uncertainty_estimates.get('prediction_variance', 0)
        score_components['epistemic'] = min(prediction_variance / 0.25, 1.0)

        # 4. 偶然不确定性分数
        predictive_entropy = uncertainty_analysis.get('predictive_entropy', 0)
        score_components['aleatoric'] = min(predictive_entropy / 2.0, 1.0)

        # 加权组合
        ambiguity_score = (
            self.config.entropy_weight * score_components['entropy'] +
            self.config.disagreement_weight * score_components['disagreement'] +
            self.config.epistemic_weight * score_components['epistemic'] +
            self.config.aleatoric_weight * score_components['aleatoric']
        )

        return min(ambiguity_score, 1.0)

    def _make_ambiguity_decision(self, uncertainty_analysis: Dict[str, Any], ambiguity_score: float) -> bool:
        """做出歧义判断决策"""
        # 基于综合分数的判断
        if ambiguity_score > self.config.entropy_threshold:
            return True

        # 基于特定指标的判断
        ensemble_entropy = uncertainty_analysis.get('ensemble_entropy', 0)
        if ensemble_entropy > self.config.ensemble_entropy_threshold:
            return True

        # 基于模型分歧的判断
        disagreement_metrics = uncertainty_analysis.get('disagreement_metrics', {})
        symmetric_kl = disagreement_metrics.get('symmetric_kl', 0)
        if symmetric_kl > self.config.disagreement_threshold:
            return True

        # 基于判决分歧的判断
        verdict_stats = uncertainty_analysis.get('verdict_stats', {})
        if not verdict_stats.get('verdict_agreement', True):
            return True

        return False

    def _update_analysis_history(self, analysis_result: Dict[str, Any]):
        """更新分析历史"""
        self.analysis_history.append(analysis_result)

        # 限制历史记录大小
        if len(self.analysis_history) > self.config.history_size:
            self.analysis_history = self.analysis_history[-self.config.history_size:]

        # 限制分歧历史大小
        if len(self.disagreement_history) > self.config.history_size:
            self.disagreement_history = self.disagreement_history[-self.config.history_size:]

    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        if not self.analysis_history:
            return {}

        stats = {}

        # 歧义检测统计
        ambiguous_count = sum(1 for a in self.analysis_history if a['is_ambiguous'])
        stats['ambiguity_rate'] = ambiguous_count / len(self.analysis_history)
        stats['total_analyzed'] = len(self.analysis_history)
        stats['ambiguous_samples'] = ambiguous_count

        # 歧义分数统计
        ambiguity_scores = [a['ambiguity_score'] for a in self.analysis_history]
        stats['ambiguity_score_stats'] = {
            'mean': np.mean(ambiguity_scores),
            'std': np.std(ambiguity_scores),
            'min': np.min(ambiguity_scores),
            'max': np.max(ambiguity_scores)
        }

        # 歧义原因统计
        all_reasons = []
        for a in self.analysis_history:
            all_reasons.extend(a['ambiguity_reasons'])

        reason_counts = defaultdict(int)
        for reason in all_reasons:
            reason_counts[reason] += 1

        stats['top_ambiguity_reasons'] = dict(sorted(reason_counts.items(),
                                                   key=lambda x: x[1], reverse=True)[:10])

        # 分歧模式分析
        if self.disagreement_history:
            disagreement_patterns = self.disagreement_measure.analyze_disagreement_patterns(
                self.disagreement_history
            )
            stats['disagreement_patterns'] = disagreement_patterns

        return stats

    def save_analysis_data(self, filepath: str = None):
        """保存分析数据"""
        if not self.config.save_analysis_data:
            return

        if filepath is None:
            filepath = os.path.join(self.config.analysis_data_path, "entropy_analysis_data.json")

        data_to_save = {
            'config': {
                'entropy_threshold': self.config.entropy_threshold,
                'disagreement_threshold': self.config.disagreement_threshold,
                'weights': {
                    'entropy_weight': self.config.entropy_weight,
                    'disagreement_weight': self.config.disagreement_weight,
                    'epistemic_weight': self.config.epistemic_weight,
                    'aleatoric_weight': self.config.aleatoric_weight
                }
            },
            'analysis_history': self.analysis_history,
            'disagreement_history': self.disagreement_history,
            'statistics': self.get_analysis_statistics()
        }

        try:
            # 转换numpy类型为Python原生类型
            def convert_numpy_types(obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, dict):
                    return {str(k): convert_numpy_types(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(item) for item in obj]
                else:
                    return obj

            converted_data = convert_numpy_types(data_to_save)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(converted_data, f, indent=2, ensure_ascii=False)
            logger.info(f"分析数据已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存分析数据失败: {e}")

    def load_analysis_data(self, filepath: str = None):
        """加载分析数据"""
        if filepath is None:
            filepath = os.path.join(self.config.analysis_data_path, "entropy_analysis_data.json")

        if not os.path.exists(filepath):
            logger.info("分析数据文件不存在，使用默认配置")
            return

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 恢复历史数据
            if 'analysis_history' in data:
                self.analysis_history = data['analysis_history']

            if 'disagreement_history' in data:
                self.disagreement_history = data['disagreement_history']

            logger.info(f"分析数据已从 {filepath} 加载")
        except Exception as e:
            logger.error(f"加载分析数据失败: {e}")
