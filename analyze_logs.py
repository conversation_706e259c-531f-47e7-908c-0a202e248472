#!/usr/bin/env python3
"""
详细分析新旧版本日志的性能差异
"""

import json
import os
import pandas as pd
from pathlib import Path
import numpy as np
from collections import defaultdict

def load_log_file(filepath):
    """加载日志文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def extract_metrics(log_data):
    """从日志数据中提取关键指标"""
    if not log_data:
        return None

    metrics = {}

    # 提取基本信息
    if 'metadata' in log_data:
        metadata = log_data['metadata']
        metrics['model'] = metadata.get('model', 'unknown')
        metrics['dataset'] = metadata.get('dataset', 'unknown')
        metrics['detector_type'] = metadata.get('detector_type', 'unknown')
        metrics['timestamp'] = metadata.get('created_at', 'unknown')
        metrics['num_samples'] = metadata.get('num_samples', 0)

    # 提取性能指标
    if 'metrics' in log_data:
        results = log_data['metrics']
        metrics['accuracy'] = results.get('accuracy', 0)
        metrics['precision'] = results.get('precision', 0)
        metrics['recall'] = results.get('recall', 0)
        metrics['f1'] = results.get('f1', 0)
        metrics['avg_processing_time'] = results.get('avg_processing_time', 0)

        # 提取混淆矩阵
        if 'confusion_matrix' in results:
            cm = results['confusion_matrix']
            if isinstance(cm, list) and len(cm) == 2:
                # [[tn, fp], [fn, tp]]
                metrics['tn'] = cm[0][0]
                metrics['fp'] = cm[0][1]
                metrics['fn'] = cm[1][0]
                metrics['tp'] = cm[1][1]

    # 分析results数组中的详细信息
    if 'results' in log_data and isinstance(log_data['results'], list):
        results_list = log_data['results']
        if len(results_list) > 0:
            # 计算统计信息
            confidences = []
            ambiguity_scores = []
            layer2_triggered_count = 0

            for result in results_list:
                if 'confidence' in result:
                    confidences.append(result['confidence'])
                if 'ambiguity_score' in result:
                    ambiguity_scores.append(result['ambiguity_score'])
                if result.get('layer2_triggered', False):
                    layer2_triggered_count += 1

            metrics['avg_confidence'] = np.mean(confidences) if confidences else 0
            metrics['avg_ambiguity_score'] = np.mean(ambiguity_scores) if ambiguity_scores else 0
            metrics['layer2_trigger_rate'] = layer2_triggered_count / len(results_list) if results_list else 0
            metrics['total_samples'] = len(results_list)

    return metrics

def analyze_logs():
    """分析所有日志文件"""
    current_logs = []
    previous_logs = []
    
    # 加载当前版本日志
    logs_dir = Path('logs')
    for log_file in logs_dir.glob('twolayerdetectionsystem_*.json'):
        log_data = load_log_file(log_file)
        if log_data:
            metrics = extract_metrics(log_data)
            if metrics:
                metrics['version'] = 'current'
                metrics['filename'] = log_file.name
                current_logs.append(metrics)
    
    # 加载之前版本日志
    prev_logs_dir = Path('previous_log')
    for log_file in prev_logs_dir.glob('twolayerdetectionsystem_*.json'):
        log_data = load_log_file(log_file)
        if log_data:
            metrics = extract_metrics(log_data)
            if metrics:
                metrics['version'] = 'previous'
                metrics['filename'] = log_file.name
                previous_logs.append(metrics)
    
    # 合并数据
    all_logs = current_logs + previous_logs
    df = pd.DataFrame(all_logs)
    
    return df, current_logs, previous_logs

def compare_performance(df):
    """对比性能差异"""
    print("=" * 80)
    print("详细性能对比分析")
    print("=" * 80)
    
    # 按模型和数据集分组对比
    for model in df['model'].unique():
        for dataset in df['dataset'].unique():
            print(f"\n模型: {model}, 数据集: {dataset}")
            print("-" * 60)
            
            current_data = df[(df['model'] == model) & 
                            (df['dataset'] == dataset) & 
                            (df['version'] == 'current')]
            
            previous_data = df[(df['model'] == model) & 
                             (df['dataset'] == dataset) & 
                             (df['version'] == 'previous')]
            
            if len(current_data) == 0 or len(previous_data) == 0:
                print("缺少对比数据")
                continue
            
            curr = current_data.iloc[0]
            prev = previous_data.iloc[0]
            
            # 核心指标对比
            metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
            for metric in metrics:
                curr_val = curr.get(metric, 0)
                prev_val = prev.get(metric, 0)
                diff = curr_val - prev_val
                diff_pct = (diff / prev_val * 100) if prev_val != 0 else 0
                
                print(f"{metric:12}: {prev_val:.4f} -> {curr_val:.4f} "
                      f"(差异: {diff:+.4f}, {diff_pct:+.2f}%)")
            
            # 配置对比
            print("\n配置变化:")
            config_keys = ['confidence_threshold', 'ambiguity_threshold', 
                          'temperature', 'use_calibration', 'use_entropy_detection']
            for key in config_keys:
                curr_val = curr.get(key, 'N/A')
                prev_val = prev.get(key, 'N/A')
                if curr_val != prev_val:
                    print(f"  {key}: {prev_val} -> {curr_val}")
            
            # 统计信息对比
            print("\n统计信息:")
            stat_keys = ['ambiguity_rate', 'avg_confidence', 'avg_entropy', 'calibration_error']
            for key in stat_keys:
                curr_val = curr.get(key, 0)
                prev_val = prev.get(key, 0)
                if curr_val != 0 or prev_val != 0:
                    diff = curr_val - prev_val
                    print(f"  {key}: {prev_val:.4f} -> {curr_val:.4f} (差异: {diff:+.4f})")

def analyze_degradation_patterns(df):
    """分析性能下降模式"""
    print("\n" + "=" * 80)
    print("性能下降模式分析")
    print("=" * 80)
    
    degradations = []
    
    for model in df['model'].unique():
        for dataset in df['dataset'].unique():
            current_data = df[(df['model'] == model) & 
                            (df['dataset'] == dataset) & 
                            (df['version'] == 'current')]
            
            previous_data = df[(df['model'] == model) & 
                             (df['dataset'] == dataset) & 
                             (df['version'] == 'previous')]
            
            if len(current_data) == 0 or len(previous_data) == 0:
                continue
            
            curr = current_data.iloc[0]
            prev = previous_data.iloc[0]
            
            # 计算各指标的下降幅度
            f1_drop = prev.get('f1', 0) - curr.get('f1', 0)
            acc_drop = prev.get('accuracy', 0) - curr.get('accuracy', 0)
            auc_drop = prev.get('auc', 0) - curr.get('auc', 0)
            
            degradations.append({
                'model': model,
                'dataset': dataset,
                'f1_drop': f1_drop,
                'acc_drop': acc_drop,
                'auc_drop': auc_drop,
                'avg_drop': (f1_drop + acc_drop + auc_drop) / 3
            })
    
    # 排序找出最严重的下降
    degradations.sort(key=lambda x: x['avg_drop'], reverse=True)
    
    print("\n最严重的性能下降 (按平均下降幅度排序):")
    for deg in degradations[:5]:
        print(f"{deg['model']} + {deg['dataset']}: "
              f"F1↓{deg['f1_drop']:.4f}, ACC↓{deg['acc_drop']:.4f}, "
              f"AUC↓{deg['auc_drop']:.4f}, 平均↓{deg['avg_drop']:.4f}")
    
    return degradations

if __name__ == "__main__":
    df, current_logs, previous_logs = analyze_logs()
    
    print(f"加载了 {len(current_logs)} 个当前版本日志")
    print(f"加载了 {len(previous_logs)} 个之前版本日志")
    
    compare_performance(df)
    degradations = analyze_degradation_patterns(df)
    
    # 保存详细分析结果
    df.to_csv('log_analysis_detailed.csv', index=False)
    print(f"\n详细分析结果已保存到 log_analysis_detailed.csv")
