#!/usr/bin/env python3
"""
自适应置信度校准模块
Adaptive Confidence Calibration Module

实现温度缩放、不确定性量化和动态阈值调整功能
基于Guo et al. (2017) "On Calibration of Modern Neural Networks"

作者: AI Assistant
日期: 2025-08-04
版本: 1.0
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.optimize import minimize_scalar
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
import json
import os

logger = logging.getLogger(__name__)

@dataclass
class CalibrationConfig:
    """置信度校准配置"""
    # 温度缩放参数
    initial_temperature: float = 1.0
    temperature_lr: float = 0.01
    temperature_max_iter: int = 50
    
    # 动态阈值参数
    base_threshold: float = 0.7
    threshold_adaptation_rate: float = 0.1
    min_threshold: float = 0.5
    max_threshold: float = 0.9
    
    # 不确定性量化参数
    mc_dropout_samples: int = 100
    uncertainty_weight_epistemic: float = 0.6
    uncertainty_weight_aleatoric: float = 0.4
    
    # 校准历史记录
    calibration_history_size: int = 1000
    save_calibration_data: bool = True
    calibration_data_path: str = "./calibration_data"

class TemperatureScaling:
    """温度缩放校准器"""
    
    def __init__(self, config: CalibrationConfig = None):
        self.config = config or CalibrationConfig()
        self.temperatures = {
            'toxigen': self.config.initial_temperature,
            'hurtlex': self.config.initial_temperature
        }
        self.calibration_history = []
        
    def calibrate_logits(self, logits: np.ndarray, model_type: str) -> np.ndarray:
        """
        使用温度缩放校准logits
        
        Args:
            logits: 原始logits输出
            model_type: 模型类型 ('toxigen' 或 'hurtlex')
            
        Returns:
            校准后的概率分布
        """
        if model_type not in self.temperatures:
            logger.warning(f"Unknown model type: {model_type}, using default temperature")
            temperature = self.config.initial_temperature
        else:
            temperature = self.temperatures[model_type]
        
        # 应用温度缩放
        scaled_logits = logits / temperature
        
        # 计算softmax概率
        exp_logits = np.exp(scaled_logits - np.max(scaled_logits))  # 数值稳定性
        probabilities = exp_logits / np.sum(exp_logits)
        
        return probabilities
    
    def optimize_temperature(self, logits_list: List[np.ndarray], 
                           true_labels: List[int], 
                           model_type: str) -> float:
        """
        优化温度参数
        
        Args:
            logits_list: logits列表
            true_labels: 真实标签列表
            model_type: 模型类型
            
        Returns:
            优化后的温度值
        """
        def negative_log_likelihood(temperature):
            total_nll = 0.0
            for logits, true_label in zip(logits_list, true_labels):
                scaled_logits = logits / temperature
                exp_logits = np.exp(scaled_logits - np.max(scaled_logits))
                probabilities = exp_logits / np.sum(exp_logits)
                
                # 避免log(0)
                prob_true = max(probabilities[true_label], 1e-10)
                total_nll -= np.log(prob_true)
            
            return total_nll / len(logits_list)
        
        # 使用scipy优化温度参数
        result = minimize_scalar(
            negative_log_likelihood,
            bounds=(0.1, 10.0),
            method='bounded'
        )
        
        optimal_temperature = result.x
        self.temperatures[model_type] = optimal_temperature
        
        logger.info(f"Optimized temperature for {model_type}: {optimal_temperature:.4f}")
        return optimal_temperature
    
    def get_temperature(self, model_type: str) -> float:
        """获取模型的温度参数"""
        return self.temperatures.get(model_type, self.config.initial_temperature)
    
    def set_temperature(self, model_type: str, temperature: float):
        """设置模型的温度参数"""
        self.temperatures[model_type] = temperature

class UncertaintyQuantifier:
    """不确定性量化器"""
    
    def __init__(self, config: CalibrationConfig = None):
        self.config = config or CalibrationConfig()
    
    def calculate_prediction_entropy(self, probabilities: np.ndarray) -> float:
        """计算预测熵"""
        # 避免log(0)
        probabilities = np.clip(probabilities, 1e-10, 1.0)
        entropy = -np.sum(probabilities * np.log2(probabilities))
        return entropy
    
    def calculate_ensemble_entropy(self, predictions_list: List[np.ndarray]) -> float:
        """计算集成预测熵"""
        mean_prediction = np.mean(predictions_list, axis=0)
        return self.calculate_prediction_entropy(mean_prediction)
    
    def calculate_kl_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """计算KL散度"""
        p = np.clip(p, 1e-10, 1.0)
        q = np.clip(q, 1e-10, 1.0)
        return np.sum(p * np.log2(p / q))
    
    def calculate_model_disagreement(self, pred1: np.ndarray, pred2: np.ndarray) -> float:
        """计算模型分歧度（对称KL散度）"""
        kl_forward = self.calculate_kl_divergence(pred1, pred2)
        kl_backward = self.calculate_kl_divergence(pred2, pred1)
        return (kl_forward + kl_backward) / 2
    
    def estimate_epistemic_uncertainty(self, predictions_list: List[np.ndarray]) -> float:
        """估计认知不确定性（模型不确定性）"""
        if len(predictions_list) < 2:
            return 0.0
        
        # 计算预测方差作为认知不确定性的代理
        predictions_array = np.array(predictions_list)
        prediction_variance = np.var(predictions_array, axis=0)
        
        # 返回方差的平均值
        return np.mean(prediction_variance)
    
    def estimate_aleatoric_uncertainty(self, predictions_list: List[np.ndarray]) -> float:
        """估计偶然不确定性（数据不确定性）"""
        # 计算平均预测熵作为偶然不确定性
        entropies = [self.calculate_prediction_entropy(pred) for pred in predictions_list]
        return np.mean(entropies)
    
    def quantify_uncertainty(self, toxigen_pred: np.ndarray, 
                           hurtlex_pred: np.ndarray) -> Dict[str, float]:
        """
        综合量化不确定性
        
        Args:
            toxigen_pred: ToxiGen模型预测概率
            hurtlex_pred: HurtLex模型预测概率
            
        Returns:
            不确定性指标字典
        """
        predictions_list = [toxigen_pred, hurtlex_pred]
        
        uncertainty_metrics = {
            # 单模型熵
            'toxigen_entropy': self.calculate_prediction_entropy(toxigen_pred),
            'hurtlex_entropy': self.calculate_prediction_entropy(hurtlex_pred),
            
            # 集成熵
            'ensemble_entropy': self.calculate_ensemble_entropy(predictions_list),
            
            # 模型分歧
            'model_disagreement': self.calculate_model_disagreement(toxigen_pred, hurtlex_pred),
            
            # 认知不确定性
            'epistemic_uncertainty': self.estimate_epistemic_uncertainty(predictions_list),
            
            # 偶然不确定性
            'aleatoric_uncertainty': self.estimate_aleatoric_uncertainty(predictions_list)
        }
        
        # 计算总不确定性
        uncertainty_metrics['total_uncertainty'] = (
            self.config.uncertainty_weight_epistemic * uncertainty_metrics['epistemic_uncertainty'] +
            self.config.uncertainty_weight_aleatoric * uncertainty_metrics['aleatoric_uncertainty']
        )
        
        return uncertainty_metrics

class AdaptiveThresholdManager:
    """自适应阈值管理器"""
    
    def __init__(self, config: CalibrationConfig = None):
        self.config = config or CalibrationConfig()
        self.current_threshold = self.config.base_threshold
        self.performance_history = []
        
    def calculate_adaptive_threshold(self, 
                                   confidence_scores: List[float],
                                   uncertainty_metrics: Dict[str, float],
                                   recent_performance: Optional[Dict[str, float]] = None) -> float:
        """
        计算自适应阈值
        
        Args:
            confidence_scores: 最近的置信度分数列表
            uncertainty_metrics: 不确定性指标
            recent_performance: 最近的性能指标
            
        Returns:
            调整后的阈值
        """
        # 基础阈值
        threshold = self.config.base_threshold
        
        # 基于置信度分布调整
        if confidence_scores:
            conf_mean = np.mean(confidence_scores)
            conf_std = np.std(confidence_scores)
            
            # 如果平均置信度较高，可以提高阈值
            confidence_adjustment = 0.1 * (conf_mean - 0.5)
            threshold += confidence_adjustment
            
            # 如果置信度方差较大，降低阈值（更保守）
            variance_adjustment = -0.1 * min(conf_std, 0.3)
            threshold += variance_adjustment
        
        # 基于不确定性调整
        total_uncertainty = uncertainty_metrics.get('total_uncertainty', 0.0)
        ensemble_entropy = uncertainty_metrics.get('ensemble_entropy', 0.0)
        
        # 高不确定性时降低阈值（更容易触发第二层）
        uncertainty_adjustment = -0.2 * min(total_uncertainty, 1.0)
        threshold += uncertainty_adjustment
        
        # 高熵时降低阈值
        entropy_adjustment = -0.15 * min(ensemble_entropy / 2.0, 1.0)
        threshold += entropy_adjustment
        
        # 基于历史性能调整
        if recent_performance:
            f1_score = recent_performance.get('f1', 0.7)
            accuracy = recent_performance.get('accuracy', 0.7)
            
            # 如果性能较差，降低阈值
            performance_score = (f1_score + accuracy) / 2
            performance_adjustment = 0.1 * (performance_score - 0.7)
            threshold += performance_adjustment
        
        # 限制阈值范围
        threshold = np.clip(threshold, self.config.min_threshold, self.config.max_threshold)
        
        # 平滑更新当前阈值
        self.current_threshold = (
            (1 - self.config.threshold_adaptation_rate) * self.current_threshold +
            self.config.threshold_adaptation_rate * threshold
        )
        
        return self.current_threshold
    
    def should_trigger_layer2(self, 
                            toxigen_confidence: float,
                            hurtlex_confidence: float,
                            uncertainty_metrics: Dict[str, float],
                            model_disagreement: bool = False) -> Tuple[bool, str]:
        """
        判断是否应该触发第二层
        
        Args:
            toxigen_confidence: ToxiGen置信度
            hurtlex_confidence: HurtLex置信度
            uncertainty_metrics: 不确定性指标
            model_disagreement: 是否存在模型分歧
            
        Returns:
            (是否触发, 触发原因)
        """
        max_confidence = max(toxigen_confidence, hurtlex_confidence)
        min_confidence = min(toxigen_confidence, hurtlex_confidence)
        confidence_gap = abs(toxigen_confidence - hurtlex_confidence)
        
        # 触发条件
        reasons = []
        
        # 1. 低置信度触发
        if max_confidence < self.current_threshold:
            reasons.append(f"low_confidence_{max_confidence:.3f}<{self.current_threshold:.3f}")
        
        # 2. 置信度差异过大
        if confidence_gap > 0.3:
            reasons.append(f"confidence_gap_{confidence_gap:.3f}")
        
        # 3. 高不确定性触发
        if uncertainty_metrics.get('total_uncertainty', 0) > 0.5:
            reasons.append(f"high_uncertainty_{uncertainty_metrics['total_uncertainty']:.3f}")
        
        # 4. 高熵触发
        if uncertainty_metrics.get('ensemble_entropy', 0) > 1.0:
            reasons.append(f"high_entropy_{uncertainty_metrics['ensemble_entropy']:.3f}")
        
        # 5. 模型分歧触发
        if model_disagreement:
            reasons.append("model_disagreement")
        
        # 6. 高模型分歧度触发
        if uncertainty_metrics.get('model_disagreement', 0) > 0.4:
            reasons.append(f"high_model_disagreement_{uncertainty_metrics['model_disagreement']:.3f}")
        
        should_trigger = len(reasons) > 0
        trigger_reason = "; ".join(reasons) if reasons else "no_trigger"
        
        return should_trigger, trigger_reason
    
    def update_performance_history(self, performance_metrics: Dict[str, float]):
        """更新性能历史"""
        self.performance_history.append(performance_metrics)
        
        # 保持历史记录大小
        if len(self.performance_history) > self.config.calibration_history_size:
            self.performance_history = self.performance_history[-self.config.calibration_history_size:]
    
    def get_recent_performance(self, window_size: int = 100) -> Optional[Dict[str, float]]:
        """获取最近的性能指标"""
        if not self.performance_history:
            return None

        recent_history = self.performance_history[-window_size:]

        # 计算平均性能
        avg_performance = {}
        for key in recent_history[0].keys():
            avg_performance[key] = np.mean([h[key] for h in recent_history])

        return avg_performance

class AdaptiveConfidenceCalibrator:
    """自适应置信度校准器主类"""

    def __init__(self, config: CalibrationConfig = None):
        self.config = config or CalibrationConfig()

        # 初始化子模块
        self.temperature_scaler = TemperatureScaling(self.config)
        self.uncertainty_quantifier = UncertaintyQuantifier(self.config)
        self.threshold_manager = AdaptiveThresholdManager(self.config)

        # 校准数据存储
        self.calibration_data = []

        # 确保校准数据目录存在
        if self.config.save_calibration_data:
            os.makedirs(self.config.calibration_data_path, exist_ok=True)

        logger.info("自适应置信度校准器初始化完成")

    def calibrate_predictions(self,
                            toxigen_logits: np.ndarray,
                            hurtlex_logits: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        校准模型预测

        Args:
            toxigen_logits: ToxiGen模型logits
            hurtlex_logits: HurtLex模型logits

        Returns:
            (校准后的ToxiGen概率, 校准后的HurtLex概率)
        """
        toxigen_calibrated = self.temperature_scaler.calibrate_logits(toxigen_logits, 'toxigen')
        hurtlex_calibrated = self.temperature_scaler.calibrate_logits(hurtlex_logits, 'hurtlex')

        return toxigen_calibrated, hurtlex_calibrated

    def analyze_ambiguity(self,
                         toxigen_pred: np.ndarray,
                         hurtlex_pred: np.ndarray,
                         text: str = None) -> Dict[str, Any]:
        """
        分析预测的歧义性

        Args:
            toxigen_pred: ToxiGen预测概率
            hurtlex_pred: HurtLex预测概率
            text: 输入文本（可选）

        Returns:
            歧义分析结果
        """
        # 量化不确定性
        uncertainty_metrics = self.uncertainty_quantifier.quantify_uncertainty(
            toxigen_pred, hurtlex_pred
        )

        # 检查模型分歧
        toxigen_verdict = np.argmax(toxigen_pred)
        hurtlex_verdict = np.argmax(hurtlex_pred)
        model_disagreement = toxigen_verdict != hurtlex_verdict

        # 计算置信度
        toxigen_confidence = np.max(toxigen_pred)
        hurtlex_confidence = np.max(hurtlex_pred)

        # 判断是否触发第二层
        should_trigger, trigger_reason = self.threshold_manager.should_trigger_layer2(
            toxigen_confidence, hurtlex_confidence, uncertainty_metrics, model_disagreement
        )

        # 计算综合歧义分数
        ambiguity_score = self._calculate_ambiguity_score(uncertainty_metrics,
                                                        toxigen_confidence,
                                                        hurtlex_confidence)

        return {
            'should_trigger_layer2': should_trigger,
            'trigger_reason': trigger_reason,
            'ambiguity_score': ambiguity_score,
            'uncertainty_metrics': uncertainty_metrics,
            'model_disagreement': model_disagreement,
            'toxigen_confidence': toxigen_confidence,
            'hurtlex_confidence': hurtlex_confidence,
            'current_threshold': self.threshold_manager.current_threshold
        }

    def _calculate_ambiguity_score(self,
                                 uncertainty_metrics: Dict[str, float],
                                 toxigen_confidence: float,
                                 hurtlex_confidence: float) -> float:
        """计算综合歧义分数"""
        # 基于不确定性的分数
        uncertainty_score = uncertainty_metrics.get('total_uncertainty', 0.0)
        entropy_score = min(uncertainty_metrics.get('ensemble_entropy', 0.0) / 2.0, 1.0)
        disagreement_score = min(uncertainty_metrics.get('model_disagreement', 0.0) / 2.0, 1.0)

        # 基于置信度的分数
        max_confidence = max(toxigen_confidence, hurtlex_confidence)
        confidence_score = 1.0 - max_confidence  # 置信度越低，歧义分数越高

        # 置信度差异分数
        confidence_gap = abs(toxigen_confidence - hurtlex_confidence)
        gap_score = min(confidence_gap / 0.5, 1.0)  # 归一化到[0,1]

        # 加权组合
        weights = {
            'uncertainty': 0.25,
            'entropy': 0.25,
            'disagreement': 0.2,
            'confidence': 0.2,
            'gap': 0.1
        }

        ambiguity_score = (
            weights['uncertainty'] * uncertainty_score +
            weights['entropy'] * entropy_score +
            weights['disagreement'] * disagreement_score +
            weights['confidence'] * confidence_score +
            weights['gap'] * gap_score
        )

        return min(ambiguity_score, 1.0)

    def update_calibration(self,
                         toxigen_logits: np.ndarray,
                         hurtlex_logits: np.ndarray,
                         true_label: int,
                         performance_metrics: Optional[Dict[str, float]] = None):
        """
        更新校准参数

        Args:
            toxigen_logits: ToxiGen模型logits
            hurtlex_logits: HurtLex模型logits
            true_label: 真实标签
            performance_metrics: 性能指标
        """
        # 存储校准数据
        calibration_sample = {
            'toxigen_logits': toxigen_logits.tolist(),
            'hurtlex_logits': hurtlex_logits.tolist(),
            'true_label': true_label
        }

        self.calibration_data.append(calibration_sample)

        # 限制校准数据大小
        if len(self.calibration_data) > self.config.calibration_history_size:
            self.calibration_data = self.calibration_data[-self.config.calibration_history_size:]

        # 更新性能历史
        if performance_metrics:
            self.threshold_manager.update_performance_history(performance_metrics)

        # 定期重新校准温度参数
        if len(self.calibration_data) % 100 == 0 and len(self.calibration_data) >= 100:
            self._recalibrate_temperatures()

    def _recalibrate_temperatures(self):
        """重新校准温度参数"""
        if len(self.calibration_data) < 50:
            return

        # 分离数据
        toxigen_logits_list = []
        hurtlex_logits_list = []
        true_labels = []

        for sample in self.calibration_data[-100:]:  # 使用最近100个样本
            toxigen_logits_list.append(np.array(sample['toxigen_logits']))
            hurtlex_logits_list.append(np.array(sample['hurtlex_logits']))
            true_labels.append(sample['true_label'])

        # 重新优化温度参数
        try:
            self.temperature_scaler.optimize_temperature(toxigen_logits_list, true_labels, 'toxigen')
            self.temperature_scaler.optimize_temperature(hurtlex_logits_list, true_labels, 'hurtlex')
            logger.info("温度参数重新校准完成")
        except Exception as e:
            logger.error(f"温度参数重新校准失败: {e}")

    def save_calibration_data(self, filepath: str = None):
        """保存校准数据"""
        if not self.config.save_calibration_data:
            return

        if filepath is None:
            filepath = os.path.join(self.config.calibration_data_path, "calibration_data.json")

        data_to_save = {
            'config': {
                'temperatures': self.temperature_scaler.temperatures,
                'current_threshold': self.threshold_manager.current_threshold
            },
            'calibration_data': self.calibration_data,
            'performance_history': self.threshold_manager.performance_history
        }

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, indent=2, ensure_ascii=False)
            logger.info(f"校准数据已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存校准数据失败: {e}")

    def load_calibration_data(self, filepath: str = None):
        """加载校准数据"""
        if filepath is None:
            filepath = os.path.join(self.config.calibration_data_path, "calibration_data.json")

        if not os.path.exists(filepath):
            logger.info("校准数据文件不存在，使用默认配置")
            return

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 恢复配置
            if 'config' in data:
                config = data['config']
                if 'temperatures' in config:
                    self.temperature_scaler.temperatures = config['temperatures']
                if 'current_threshold' in config:
                    self.threshold_manager.current_threshold = config['current_threshold']

            # 恢复校准数据
            if 'calibration_data' in data:
                self.calibration_data = data['calibration_data']

            # 恢复性能历史
            if 'performance_history' in data:
                self.threshold_manager.performance_history = data['performance_history']

            logger.info(f"校准数据已从 {filepath} 加载")
        except Exception as e:
            logger.error(f"加载校准数据失败: {e}")

    def get_calibration_stats(self) -> Dict[str, Any]:
        """获取校准统计信息"""
        return {
            'temperatures': self.temperature_scaler.temperatures.copy(),
            'current_threshold': self.threshold_manager.current_threshold,
            'calibration_samples': len(self.calibration_data),
            'performance_history_size': len(self.threshold_manager.performance_history),
            'recent_performance': self.threshold_manager.get_recent_performance()
        }
